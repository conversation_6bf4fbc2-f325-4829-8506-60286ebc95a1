# 🚀 AugmentWriter

> AI驱动的智能小说创作平台，集成Google Gemini AI，让创作更加高效和有趣

[![CI/CD](https://github.com/your-username/augmentWriter/workflows/CI/CD%20Pipeline/badge.svg)](https://github.com/your-username/augmentWriter/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-20232A?logo=react&logoColor=61DAFB)](https://reactjs.org/)

## ✨ 核心特性

### 🤖 AI智能辅助
- **智能续写** - 基于上下文和角色设定的智能内容生成
- **风格分析** - 自动分析文本风格，保持写作一致性
- **大纲生成** - AI辅助生成结构化的故事大纲
- **角色生成** - 智能创建角色档案和关系网络

### 📝 写作工具
- **富文本编辑器** - 基于Tiptap的现代化编辑体验
- **章节管理** - 灵活的章节组织和排序
- **实时保存** - 自动保存，永不丢失创作内容
- **版本控制** - 完整的修改历史和版本回退

### 👥 协作功能
- **实时协作** - 多人同时编辑，实时同步
- **评论系统** - 章节级别的评论和反馈
- **权限管理** - 灵活的协作权限控制

### 📊 项目管理
- **进度跟踪** - 字数统计和写作进度可视化
- **目标设定** - 每日写作目标和提醒
- **数据分析** - 写作习惯和效率分析

## 🛠️ 技术栈

### 前端
- **React 18** + **TypeScript** - 现代化的用户界面
- **Vite** - 快速的开发构建工具
- **Tailwind CSS** - 实用优先的CSS框架
- **Zustand** - 轻量级状态管理
- **React Query** - 数据获取和缓存
- **Tiptap** - 富文本编辑器

### 后端
- **Node.js** + **Express** + **TypeScript** - 服务端API
- **Prisma** - 现代化的数据库ORM
- **PostgreSQL** - 可靠的关系型数据库
- **Redis** - 高性能缓存和会话存储
- **Socket.IO** - 实时通信

### AI集成
- **Google Gemini API** - 主力文本生成模型
- **Ollama** (可选) - 本地AI模型支持
- **ComfyUI** (可选) - 图像生成工具

### 部署
- **Docker** + **Docker Compose** - 容器化部署
- **Nginx** - 反向代理和静态文件服务
- **GitHub Actions** - CI/CD自动化

## 🚀 快速开始

### 环境要求
- Node.js 18+
- PostgreSQL 15+
- Redis 7+
- Docker (可选)

### 1. 克隆项目
```bash
git clone https://github.com/your-username/augmentWriter.git
cd augmentWriter
```

### 2. 快速安装
```bash
# 使用自动化安装脚本
./setup.sh

# 或手动安装
npm install
```

### 3. 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

必需的环境变量：
```env
# 数据库
DATABASE_URL="postgresql://postgres:password@localhost:5432/augment_writer"
REDIS_URL="redis://localhost:6379"

# JWT密钥
JWT_SECRET="your-super-secret-jwt-key"

# Gemini AI
GEMINI_API_KEY="your-gemini-api-key"
```

### 4. 启动开发环境
```bash
# 使用开发脚本（推荐）
./scripts/dev.sh start

# 或手动启动
npm run docker:dev  # 启动数据库
npm run db:migrate  # 运行数据库迁移
npm run dev         # 启动开发服务器
```

### 5. 访问应用
- 前端: http://localhost:8181
- 后端API: http://localhost:8989
- API文档: http://localhost:8989/api/docs

## 📖 使用指南

### 开发命令
```bash
# 开发环境管理
./scripts/dev.sh start    # 启动完整开发环境（智能检测已运行服务）
./scripts/dev.sh stop     # 停止所有服务
./scripts/dev.sh restart  # 重启所有服务
./scripts/dev.sh status   # 查看服务状态
./scripts/dev.sh logs     # 查看日志

# 传统方式
npm run dev              # 启动开发服务器
npm run dev:env start    # 启动完整开发环境
npm run dev:env stop     # 停止开发环境

# 构建
npm run build            # 构建所有模块
npm run build:shared     # 构建共享模块
npm run client:build     # 构建前端
npm run server:build     # 构建后端

# 测试
npm run test             # 运行所有测试
npm run test:coverage    # 运行测试并生成覆盖率报告
npm run test:ui          # 启动测试UI界面

# 代码质量
npm run lint             # 代码检查
npm run type-check       # 类型检查
npm run format           # 代码格式化
npm run format:check     # 检查代码格式

# 数据库
npm run db:migrate       # 运行数据库迁移
npm run db:seed          # 填充种子数据
npm run db:reset         # 重置数据库

# Docker
npm run docker:dev       # 启动开发环境容器
npm run docker:build     # 构建生产镜像
npm run docker:up        # 启动生产环境
npm run deploy           # 部署到生产环境
```

### 测试账户
开发环境提供了测试账户：
- 邮箱: `<EMAIL>`
- 密码: `password123`

## 🏗️ 项目结构

```
augmentWriter/
├── client/                 # 前端应用
│   ├── src/
│   │   ├── components/     # React组件
│   │   ├── pages/          # 页面组件
│   │   ├── hooks/          # 自定义Hook
│   │   ├── stores/         # 状态管理
│   │   ├── services/       # API服务
│   │   └── utils/          # 工具函数
│   └── package.json
├── server/                 # 后端应用
│   ├── src/
│   │   ├── routes/         # API路由
│   │   ├── services/       # 业务逻辑
│   │   ├── middleware/     # 中间件
│   │   └── utils/          # 工具函数
│   ├── prisma/             # 数据库Schema
│   └── package.json
├── shared/                 # 共享类型和工具
│   ├── src/
│   │   ├── types/          # TypeScript类型定义
│   │   └── utils/          # 共享工具函数
│   └── package.json
├── scripts/                # 部署和开发脚本
├── .github/workflows/      # CI/CD配置
├── docker-compose.yml      # 生产环境Docker配置
├── docker-compose.dev.yml  # 开发环境Docker配置
└── package.json           # 根项目配置
```

## 🧪 测试

项目采用TDD（测试驱动开发）方法，包含完整的测试套件：

```bash
# 运行所有测试
npm run test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 启动测试UI界面
npm run test:ui

# 运行特定模块的测试
npm run client:test
npm run server:test
npm run shared:test
```

测试覆盖率目标：
- 分支覆盖率: 80%+
- 函数覆盖率: 80%+
- 行覆盖率: 80%+
- 语句覆盖率: 80%+

## 🚀 部署

### 开发环境部署
```bash
# 启动开发环境
./scripts/dev.sh start

# 停止开发环境
./scripts/dev.sh stop
```

### 生产环境部署
```bash
# 使用Docker Compose部署
./scripts/deploy.sh

# 或手动部署
docker-compose up -d
```

### 环境变量配置
生产环境需要配置以下环境变量：
- `DATABASE_URL` - PostgreSQL连接字符串
- `REDIS_URL` - Redis连接字符串
- `JWT_SECRET` - JWT签名密钥
- `GEMINI_API_KEY` - Google Gemini API密钥

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 开发流程
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范
- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 和 Prettier 配置
- 编写单元测试和集成测试
- 提交信息遵循 Conventional Commits 规范

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Google Gemini](https://ai.google.dev/) - 强大的AI文本生成能力
- [Ollama](https://ollama.ai/) - 本地AI模型支持
- [Prisma](https://www.prisma.io/) - 现代化的数据库工具
- [React](https://reactjs.org/) - 用户界面库
- [Tailwind CSS](https://tailwindcss.com/) - CSS框架

---

<div align="center">
  <p>用 ❤️ 和 ☕ 制作</p>
  <p>© 2024 AugmentWriter Team. All rights reserved.</p>
</div>
