#!/bin/bash

# 测试开发脚本的修复
# 验证重复启动不会导致错误

set -e

echo "🧪 测试开发脚本修复..."
echo ""

# 测试1: 首次启动
echo "📋 测试1: 首次启动数据库服务"
./scripts/dev.sh db
echo ""

# 测试2: 重复启动（应该不会报错）
echo "📋 测试2: 重复启动数据库服务（应该检测到已运行）"
./scripts/dev.sh db
echo ""

# 测试3: 检查状态
echo "📋 测试3: 检查服务状态"
./scripts/dev.sh status
echo ""

# 测试4: 停止服务
echo "📋 测试4: 停止服务"
./scripts/dev.sh stop
echo ""

# 测试5: 再次检查状态
echo "📋 测试5: 停止后检查状态"
./scripts/dev.sh status
echo ""

echo "✅ 测试完成！"
echo ""
echo "💡 使用说明:"
echo "  ./scripts/dev.sh start   - 启动完整开发环境"
echo "  ./scripts/dev.sh status  - 查看服务状态"
echo "  ./scripts/dev.sh stop    - 停止所有服务"
echo "  ./scripts/dev.sh restart - 重启服务"
echo ""
