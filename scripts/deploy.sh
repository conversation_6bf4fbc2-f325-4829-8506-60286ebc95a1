#!/bin/bash

# AugmentWriter 部署脚本
# 用于生产环境部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的环境变量
check_env_vars() {
    log_info "检查环境变量..."
    
    required_vars=(
        "POSTGRES_PASSWORD"
        "JWT_SECRET"
        "GEMINI_API_KEY"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "环境变量 $var 未设置"
            exit 1
        fi
    done
    
    log_success "环境变量检查通过"
}

# 检查 Docker 和 Docker Compose
check_docker() {
    log_info "检查 Docker 环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    log_success "Docker 环境检查通过"
}

# 拉取最新代码
pull_latest_code() {
    log_info "拉取最新代码..."
    
    if [ -d ".git" ]; then
        git pull origin main
        log_success "代码更新完成"
    else
        log_warning "不是 Git 仓库，跳过代码拉取"
    fi
}

# 构建 Docker 镜像
build_images() {
    log_info "构建 Docker 镜像..."
    
    docker-compose build --no-cache
    
    log_success "Docker 镜像构建完成"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 确保数据库服务运行
    docker-compose up -d postgres redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 10
    
    # 运行迁移
    docker-compose run --rm app npm run db:migrate
    
    log_success "数据库迁移完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."

    # 检查是否有运行中的服务
    if docker-compose ps -q | grep -q .; then
        log_info "发现运行中的服务，正在重启..."
        docker-compose down
        sleep 2
    fi

    # 启动新服务
    log_info "启动 Docker 服务..."
    docker-compose up -d

    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10

    log_success "服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:${PORT:-8989}/health > /dev/null 2>&1; then
            log_success "健康检查通过"
            return 0
        fi
        
        log_info "健康检查失败，重试 ($attempt/$max_attempts)..."
        sleep 10
        ((attempt++))
    done
    
    log_error "健康检查失败，部署可能有问题"
    return 1
}

# 清理旧镜像
cleanup() {
    log_info "清理旧 Docker 镜像..."
    
    docker image prune -f
    docker volume prune -f
    
    log_success "清理完成"
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."
    
    backup_dir="./backups"
    mkdir -p "$backup_dir"
    
    timestamp=$(date +"%Y%m%d_%H%M%S")
    backup_file="$backup_dir/backup_$timestamp.sql"
    
    docker-compose exec -T postgres pg_dump -U postgres augment_writer > "$backup_file"
    
    # 压缩备份文件
    gzip "$backup_file"
    
    log_success "数据库备份完成: $backup_file.gz"
    
    # 保留最近 7 天的备份
    find "$backup_dir" -name "backup_*.sql.gz" -mtime +7 -delete
}

# 回滚函数
rollback() {
    log_warning "开始回滚..."
    
    # 停止当前服务
    docker-compose down
    
    # 恢复到上一个版本（这里需要根据实际情况实现）
    log_info "回滚到上一个版本..."
    
    # 重新启动服务
    docker-compose up -d
    
    log_success "回滚完成"
}

# 主部署流程
main() {
    log_info "开始部署 AugmentWriter..."
    
    # 检查环境
    check_env_vars
    check_docker
    
    # 备份数据库
    backup_database
    
    # 更新代码
    pull_latest_code
    
    # 构建和部署
    build_images
    run_migrations
    start_services
    
    # 健康检查
    if health_check; then
        log_success "部署成功完成！"
        cleanup
    else
        log_error "部署失败，考虑回滚"
        read -p "是否要回滚到上一个版本？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rollback
        fi
        exit 1
    fi
}

# 处理命令行参数
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        rollback
        ;;
    "backup")
        backup_database
        ;;
    "health")
        health_check
        ;;
    *)
        echo "用法: $0 {deploy|rollback|backup|health}"
        echo "  deploy  - 执行完整部署"
        echo "  rollback - 回滚到上一个版本"
        echo "  backup  - 备份数据库"
        echo "  health  - 执行健康检查"
        exit 1
        ;;
esac
