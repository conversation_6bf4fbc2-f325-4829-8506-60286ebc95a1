#!/bin/bash

# 数据库故障排除脚本
# 用于诊断和修复数据库连接问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 状态
check_docker() {
    log_info "检查 Docker 状态..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        return 1
    fi
    
    if ! docker ps >/dev/null 2>&1; then
        log_error "Docker 未运行或权限不足"
        return 1
    fi
    
    log_success "Docker 运行正常"
}

# 检查 PostgreSQL 容器
check_postgres_container() {
    log_info "检查 PostgreSQL 容器状态..."
    
    local container_status=$(docker ps -a --format "table {{.Names}}\t{{.Status}}" | grep "postgres-dev" || echo "")
    
    if [ -z "$container_status" ]; then
        log_warning "PostgreSQL 容器不存在"
        return 1
    fi
    
    echo "容器状态: $container_status"
    
    if echo "$container_status" | grep -q "healthy"; then
        log_success "PostgreSQL 容器健康"
        return 0
    elif echo "$container_status" | grep -q "Up"; then
        log_warning "PostgreSQL 容器运行中但可能未就绪"
        return 2
    else
        log_error "PostgreSQL 容器未运行"
        return 1
    fi
}

# 检查端口占用
check_port() {
    log_info "检查端口 5432 状态..."
    
    if lsof -i :5432 >/dev/null 2>&1; then
        log_info "端口 5432 被占用:"
        lsof -i :5432
        return 0
    else
        log_warning "端口 5432 未被占用"
        return 1
    fi
}

# 检查环境变量
check_env_vars() {
    log_info "检查环境变量..."
    
    if [ ! -f "server/.env" ]; then
        log_error "server/.env 文件不存在"
        return 1
    fi
    
    local database_url=$(grep "DATABASE_URL" server/.env | cut -d'=' -f2 | tr -d '"')
    if [ -z "$database_url" ]; then
        log_error "DATABASE_URL 未设置"
        return 1
    fi
    
    log_info "DATABASE_URL: $database_url"
    log_success "环境变量检查通过"
}

# 测试数据库连接
test_connection() {
    log_info "测试数据库连接..."
    
    cd server
    if npx prisma validate >/dev/null 2>&1; then
        log_success "Prisma 配置验证通过"
    else
        log_error "Prisma 配置验证失败"
        cd ..
        return 1
    fi
    
    if npx prisma db push --accept-data-loss >/dev/null 2>&1; then
        log_success "数据库连接测试通过"
        cd ..
        return 0
    else
        log_error "数据库连接测试失败"
        cd ..
        return 1
    fi
}

# 重置数据库
reset_database() {
    log_warning "重置数据库..."
    
    # 停止容器
    log_info "停止 PostgreSQL 容器..."
    docker-compose -f docker-compose.dev.yml down postgres-dev >/dev/null 2>&1 || true
    
    # 删除数据卷
    log_info "删除数据卷..."
    docker volume rm augumentwriter_postgres_dev_data >/dev/null 2>&1 || true
    
    # 重新启动
    log_info "重新启动 PostgreSQL 容器..."
    docker-compose -f docker-compose.dev.yml up -d postgres-dev
    
    # 等待启动
    log_info "等待容器启动..."
    sleep 10
    
    # 等待健康检查
    local max_attempts=30
    local attempt=1
    while [ $attempt -le $max_attempts ]; do
        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep "postgres-dev" | grep -q "healthy"; then
            log_success "PostgreSQL 容器重置完成"
            return 0
        fi
        sleep 2
        ((attempt++))
    done
    
    log_error "PostgreSQL 容器重置失败"
    return 1
}

# 显示日志
show_logs() {
    log_info "显示 PostgreSQL 容器日志..."
    docker logs augumentwriter-postgres-dev-1 --tail 50
}

# 主诊断流程
diagnose() {
    log_info "开始数据库故障诊断..."
    echo ""
    
    # 1. 检查 Docker
    if ! check_docker; then
        log_error "请先安装并启动 Docker"
        return 1
    fi
    echo ""
    
    # 2. 检查环境变量
    if ! check_env_vars; then
        log_error "请检查环境变量配置"
        return 1
    fi
    echo ""
    
    # 3. 检查容器状态
    local container_check_result
    check_postgres_container
    container_check_result=$?
    echo ""
    
    # 4. 检查端口
    check_port
    echo ""
    
    # 5. 测试连接
    if test_connection; then
        log_success "数据库连接正常！"
        return 0
    fi
    echo ""
    
    # 6. 根据检查结果提供建议
    if [ $container_check_result -eq 1 ]; then
        log_info "建议: 启动 PostgreSQL 容器"
        echo "  docker-compose -f docker-compose.dev.yml up -d postgres-dev"
    elif [ $container_check_result -eq 2 ]; then
        log_info "建议: 等待容器完全启动或重置数据库"
        echo "  ./scripts/db-troubleshoot.sh reset"
    else
        log_info "建议: 查看容器日志或重置数据库"
        echo "  ./scripts/db-troubleshoot.sh logs"
        echo "  ./scripts/db-troubleshoot.sh reset"
    fi
}

# 主函数
main() {
    case "${1:-diagnose}" in
        "diagnose")
            diagnose
            ;;
        "reset")
            reset_database
            ;;
        "logs")
            show_logs
            ;;
        "test")
            test_connection
            ;;
        *)
            echo "用法: $0 {diagnose|reset|logs|test}"
            echo "  diagnose - 运行完整诊断"
            echo "  reset    - 重置数据库"
            echo "  logs     - 显示容器日志"
            echo "  test     - 测试数据库连接"
            exit 1
            ;;
    esac
}

main "$@"
