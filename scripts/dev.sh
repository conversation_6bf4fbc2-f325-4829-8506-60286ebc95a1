#!/bin/bash

# AugmentWriter 开发环境脚本
# 用于快速启动开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查开发环境依赖..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    # 检查包管理器
    if command -v yarn &> /dev/null; then
        PACKAGE_MANAGER="yarn"
        log_info "使用 Yarn: $(yarn -v)"
    elif command -v npm &> /dev/null; then
        PACKAGE_MANAGER="npm"
        log_info "使用 npm: $(npm -v)"
    else
        log_error "未找到 npm 或 yarn"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn install
    else
        npm install
    fi
    
    log_success "依赖安装完成"
}

# 检查服务是否运行
check_service_running() {
    local service_name=$1
    if command -v docker &> /dev/null; then
        docker ps --format "table {{.Names}}" | grep -q "$service_name" 2>/dev/null
    else
        return 1
    fi
}

# 检查端口是否被占用
check_port_in_use() {
    local port=$1
    if command -v lsof &> /dev/null; then
        lsof -i :$port >/dev/null 2>&1
    elif command -v netstat &> /dev/null; then
        netstat -an | grep ":$port " >/dev/null 2>&1
    else
        return 1
    fi
}

# 启动数据库服务
start_database() {
    log_info "检查数据库服务状态..."

    if ! command -v docker-compose &> /dev/null; then
        log_warning "Docker Compose 未安装，请手动启动 PostgreSQL 和 Redis"
        return 0
    fi

    # 检查 PostgreSQL 是否已运行
    local postgres_running=false
    local redis_running=false

    if check_service_running "postgres-dev" || check_port_in_use 5432; then
        log_info "PostgreSQL 服务已在运行"
        postgres_running=true
    fi

    if check_service_running "redis-dev" || check_port_in_use 6379; then
        log_info "Redis 服务已在运行"
        redis_running=true
    fi

    # 只启动未运行的服务
    if [ "$postgres_running" = false ] && [ "$redis_running" = false ]; then
        log_info "启动数据库服务..."
        docker-compose -f docker-compose.dev.yml up -d postgres-dev redis-dev
    elif [ "$postgres_running" = false ]; then
        log_info "启动 PostgreSQL 服务..."
        docker-compose -f docker-compose.dev.yml up -d postgres-dev
    elif [ "$redis_running" = false ]; then
        log_info "启动 Redis 服务..."
        docker-compose -f docker-compose.dev.yml up -d redis-dev
    fi

    # 等待数据库启动（只有在新启动服务时才等待）
    if [ "$postgres_running" = false ] || [ "$redis_running" = false ]; then
        log_info "等待数据库服务启动..."

        # 等待 PostgreSQL
        if [ "$postgres_running" = false ]; then
            log_info "等待 PostgreSQL 完全启动..."
            local max_attempts=60
            local attempt=1
            while [ $attempt -le $max_attempts ]; do
                # 检查容器健康状态
                if docker ps --format "table {{.Names}}\t{{.Status}}" | grep "postgres-dev" | grep -q "healthy"; then
                    log_info "PostgreSQL 容器健康检查通过"
                    break
                elif check_port_in_use 5432; then
                    # 如果端口开放，再等待几秒确保数据库完全初始化
                    log_info "PostgreSQL 端口已开放，等待数据库初始化..."
                    sleep 5
                    break
                fi
                log_info "等待 PostgreSQL 启动... ($attempt/$max_attempts)"
                sleep 2
                ((attempt++))
            done

            if [ $attempt -gt $max_attempts ]; then
                log_error "PostgreSQL 启动超时"
                return 1
            fi

            # 额外等待确保数据库完全就绪
            log_info "验证数据库连接..."
            sleep 3
        fi

        # 等待 Redis
        if [ "$redis_running" = false ]; then
            local max_attempts=15
            local attempt=1
            while [ $attempt -le $max_attempts ]; do
                if check_port_in_use 6379; then
                    log_info "Redis 已就绪"
                    break
                fi
                log_info "等待 Redis 启动... ($attempt/$max_attempts)"
                sleep 1
                ((attempt++))
            done

            if [ $attempt -gt $max_attempts ]; then
                log_error "Redis 启动超时"
                return 1
            fi
        fi
    fi

    log_success "数据库服务就绪"
}

# 测试数据库连接
test_database_connection() {
    log_info "测试数据库连接..."

    cd server
    local max_attempts=10
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if npx prisma db push --accept-data-loss >/dev/null 2>&1; then
            log_success "数据库连接测试通过"
            cd ..
            return 0
        fi

        log_info "数据库连接测试失败，重试... ($attempt/$max_attempts)"
        sleep 3
        ((attempt++))
    done

    log_error "数据库连接测试失败"
    cd ..
    return 1
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."

    # 先测试连接
    if ! test_database_connection; then
        log_error "数据库连接失败，跳过迁移"
        return 1
    fi

    cd server
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn prisma migrate dev
        yarn prisma db seed
    else
        npm run db:migrate
        npm run db:seed
    fi
    cd ..

    log_success "数据库迁移完成"
}

# 构建共享包
build_shared() {
    log_info "构建共享包..."
    
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn workspace augment-writer-shared build
    else
        npm run build --workspace=shared
    fi
    
    log_success "共享包构建完成"
}

# 检查进程是否运行
check_process_running() {
    local pid_file=$1
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# 启动开发服务器
start_dev_servers() {
    log_info "检查开发服务器状态..."

    # 创建日志目录
    mkdir -p logs

    local server_running=false
    local client_running=false

    # 检查后端服务器
    if check_process_running "logs/server.pid"; then
        log_info "后端服务器已在运行 (PID: $(cat logs/server.pid))"
        server_running=true
    elif check_port_in_use ${PORT:-8989}; then
        log_info "后端端口 ${PORT:-8989} 已被占用"
        server_running=true
    fi

    # 检查前端服务器
    if check_process_running "logs/client.pid"; then
        log_info "前端服务器已在运行 (PID: $(cat logs/client.pid))"
        client_running=true
    elif check_port_in_use ${CLIENT_PORT:-8181}; then
        log_info "前端端口 ${CLIENT_PORT:-8181} 已被占用"
        client_running=true
    fi

    # 启动后端服务器（如果未运行）
    if [ "$server_running" = false ]; then
        log_info "启动后端服务器..."
        cd server
        if [ "$PACKAGE_MANAGER" = "yarn" ]; then
            yarn dev > ../logs/server.log 2>&1 &
        else
            npm run dev > ../logs/server.log 2>&1 &
        fi
        SERVER_PID=$!
        echo $SERVER_PID > ../logs/server.pid
        cd ..

        # 等待后端启动
        log_info "等待后端服务器启动..."
        local max_attempts=30
        local attempt=1
        while [ $attempt -le $max_attempts ]; do
            if check_port_in_use ${PORT:-8989}; then
                log_info "后端服务器已就绪"
                break
            fi
            sleep 2
            ((attempt++))
        done

        if [ $attempt -gt $max_attempts ]; then
            log_error "后端服务器启动超时"
            return 1
        fi
    fi

    # 启动前端服务器（如果未运行）
    if [ "$client_running" = false ]; then
        log_info "启动前端服务器..."
        cd client
        if [ "$PACKAGE_MANAGER" = "yarn" ]; then
            yarn dev > ../logs/client.log 2>&1 &
        else
            npm run dev > ../logs/client.log 2>&1 &
        fi
        CLIENT_PID=$!
        echo $CLIENT_PID > ../logs/client.pid
        cd ..

        # 等待前端启动
        log_info "等待前端服务器启动..."
        local max_attempts=30
        local attempt=1
        while [ $attempt -le $max_attempts ]; do
            if check_port_in_use ${CLIENT_PORT:-8181}; then
                log_info "前端服务器已就绪"
                break
            fi
            sleep 2
            ((attempt++))
        done

        if [ $attempt -gt $max_attempts ]; then
            log_error "前端服务器启动超时"
            return 1
        fi
    fi

    log_success "开发服务器就绪"
    log_info "前端地址: http://localhost:${CLIENT_PORT:-8181}"
    log_info "后端地址: http://localhost:${PORT:-8989}"
    log_info "API文档: http://localhost:${PORT:-8989}/api/docs"
}

# 停止开发服务器
stop_dev_servers() {
    log_info "停止开发服务器..."

    local stopped_any=false

    # 停止前端服务器
    if [ -f logs/client.pid ]; then
        CLIENT_PID=$(cat logs/client.pid)
        if kill -0 $CLIENT_PID 2>/dev/null; then
            log_info "停止前端服务器 (PID: $CLIENT_PID)..."
            kill $CLIENT_PID

            # 等待进程结束
            local attempt=1
            while [ $attempt -le 10 ] && kill -0 $CLIENT_PID 2>/dev/null; do
                sleep 1
                ((attempt++))
            done

            # 如果进程仍在运行，强制杀死
            if kill -0 $CLIENT_PID 2>/dev/null; then
                log_warning "强制停止前端服务器..."
                kill -9 $CLIENT_PID 2>/dev/null || true
            fi

            log_info "前端服务器已停止"
            stopped_any=true
        fi
        rm -f logs/client.pid
    else
        # 尝试通过端口查找并停止进程
        if check_port_in_use ${CLIENT_PORT:-8181}; then
            log_info "发现前端端口 ${CLIENT_PORT:-8181} 被占用，尝试停止..."
            if command -v lsof &> /dev/null; then
                local pid=$(lsof -ti :${CLIENT_PORT:-8181})
                if [ -n "$pid" ]; then
                    kill $pid 2>/dev/null || true
                    log_info "已停止占用前端端口的进程"
                    stopped_any=true
                fi
            fi
        fi
    fi

    # 停止后端服务器
    if [ -f logs/server.pid ]; then
        SERVER_PID=$(cat logs/server.pid)
        if kill -0 $SERVER_PID 2>/dev/null; then
            log_info "停止后端服务器 (PID: $SERVER_PID)..."
            kill $SERVER_PID

            # 等待进程结束
            local attempt=1
            while [ $attempt -le 10 ] && kill -0 $SERVER_PID 2>/dev/null; do
                sleep 1
                ((attempt++))
            done

            # 如果进程仍在运行，强制杀死
            if kill -0 $SERVER_PID 2>/dev/null; then
                log_warning "强制停止后端服务器..."
                kill -9 $SERVER_PID 2>/dev/null || true
            fi

            log_info "后端服务器已停止"
            stopped_any=true
        fi
        rm -f logs/server.pid
    else
        # 尝试通过端口查找并停止进程
        if check_port_in_use ${PORT:-8989}; then
            log_info "发现后端端口 ${PORT:-8989} 被占用，尝试停止..."
            if command -v lsof &> /dev/null; then
                local pid=$(lsof -ti :${PORT:-8989})
                if [ -n "$pid" ]; then
                    kill $pid 2>/dev/null || true
                    log_info "已停止占用后端端口的进程"
                    stopped_any=true
                fi
            fi
        fi
    fi

    # 停止数据库服务
    if command -v docker-compose &> /dev/null; then
        log_info "停止数据库服务..."
        docker-compose -f docker-compose.dev.yml down
        log_info "数据库服务已停止"
        stopped_any=true
    fi

    if [ "$stopped_any" = true ]; then
        log_success "服务已停止"
    else
        log_info "没有发现运行中的服务"
    fi
}

# 重置开发环境
reset_dev_env() {
    log_warning "重置开发环境..."
    
    # 停止服务
    stop_dev_servers
    
    # 清理数据库
    if command -v docker-compose &> /dev/null; then
        docker-compose -f docker-compose.dev.yml down -v
        log_info "数据库数据已清理"
    fi
    
    # 重新安装依赖
    install_dependencies
    
    # 重新构建
    build_shared
    
    # 重新启动
    start_database
    run_migrations
    
    log_success "开发环境重置完成"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn test
    else
        npm test
    fi
    
    log_success "测试完成"
}

# 运行代码检查
run_lint() {
    log_info "运行代码检查..."
    
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn lint
        yarn type-check
    else
        npm run lint
        npm run type-check
    fi
    
    log_success "代码检查完成"
}

# 显示日志
show_logs() {
    local service=${1:-all}
    
    case $service in
        "server")
            if [ -f logs/server.log ]; then
                tail -f logs/server.log
            else
                log_error "服务器日志文件不存在"
            fi
            ;;
        "client")
            if [ -f logs/client.log ]; then
                tail -f logs/client.log
            else
                log_error "客户端日志文件不存在"
            fi
            ;;
        "all"|*)
            if [ -f logs/server.log ] && [ -f logs/client.log ]; then
                tail -f logs/server.log logs/client.log
            else
                log_error "日志文件不存在"
            fi
            ;;
    esac
}

# 显示服务状态
show_status() {
    log_info "检查服务状态..."
    echo ""

    # 检查数据库服务
    echo "📊 数据库服务:"
    if check_service_running "postgres-dev" || check_port_in_use 5432; then
        echo "  ✅ PostgreSQL: 运行中 (端口 5432)"
    else
        echo "  ❌ PostgreSQL: 未运行"
    fi

    if check_service_running "redis-dev" || check_port_in_use 6379; then
        echo "  ✅ Redis: 运行中 (端口 6379)"
    else
        echo "  ❌ Redis: 未运行"
    fi

    echo ""
    echo "🚀 应用服务:"

    # 检查后端服务器
    if check_process_running "logs/server.pid"; then
        local pid=$(cat logs/server.pid)
        echo "  ✅ 后端服务器: 运行中 (PID: $pid, 端口: ${PORT:-8989})"
    elif check_port_in_use ${PORT:-8989}; then
        echo "  ⚠️  后端服务器: 端口 ${PORT:-8989} 被占用 (非脚本启动)"
    else
        echo "  ❌ 后端服务器: 未运行"
    fi

    # 检查前端服务器
    if check_process_running "logs/client.pid"; then
        local pid=$(cat logs/client.pid)
        echo "  ✅ 前端服务器: 运行中 (PID: $pid, 端口: ${CLIENT_PORT:-8181})"
    elif check_port_in_use ${CLIENT_PORT:-8181}; then
        echo "  ⚠️  前端服务器: 端口 ${CLIENT_PORT:-8181} 被占用 (非脚本启动)"
    else
        echo "  ❌ 前端服务器: 未运行"
    fi

    echo ""
    echo "🔗 访问地址:"
    echo "  前端: http://localhost:${CLIENT_PORT:-8181}"
    echo "  后端: http://localhost:${PORT:-8989}"
    echo "  API文档: http://localhost:${PORT:-8989}/api/docs"
    echo ""
}

# 显示帮助信息
show_help() {
    echo "AugmentWriter 开发环境管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     - 启动完整开发环境"
    echo "  stop      - 停止所有服务"
    echo "  restart   - 重启所有服务"
    echo "  status    - 显示服务状态"
    echo "  reset     - 重置开发环境"
    echo "  test      - 运行测试"
    echo "  lint      - 运行代码检查"
    echo "  logs      - 显示日志 [server|client|all]"
    echo "  db        - 启动数据库服务"
    echo "  migrate   - 运行数据库迁移"
    echo "  build     - 构建共享包"
    echo "  help      - 显示此帮助信息"
    echo ""
}

# 主函数
main() {
    case "${1:-start}" in
        "start")
            check_dependencies
            install_dependencies
            build_shared
            start_database
            run_migrations
            start_dev_servers
            echo ""
            log_success "开发环境启动完成！"
            echo ""
            log_info "可用命令:"
            log_info "  $0 stop     - 停止服务"
            log_info "  $0 logs     - 查看日志"
            log_info "  $0 test     - 运行测试"
            echo ""
            ;;
        "stop")
            stop_dev_servers
            ;;
        "restart")
            stop_dev_servers
            sleep 2
            start_database
            run_migrations
            start_dev_servers
            ;;
        "status")
            show_status
            ;;
        "reset")
            reset_dev_env
            ;;
        "test")
            run_tests
            ;;
        "lint")
            run_lint
            ;;
        "logs")
            show_logs $2
            ;;
        "db")
            start_database
            ;;
        "migrate")
            run_migrations
            ;;
        "build")
            build_shared
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 捕获 Ctrl+C 信号
trap 'log_info "收到中断信号，正在停止服务..."; stop_dev_servers; exit 0' INT

# 运行主函数
main "$@"
