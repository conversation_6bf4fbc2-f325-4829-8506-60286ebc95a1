{"name": "augment-writer", "version": "1.0.0", "private": true, "description": "AI-powered novel writing platform with Gemini integration", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "build": "npm run build:shared && npm run client:build && npm run server:build", "build:shared": "cd shared && npm run build", "client:build": "cd client && npm run build", "server:build": "cd server && npm run build", "start": "cd server && npm start", "test": "vitest", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "client:test": "cd client && npm test", "server:test": "cd server && npm test", "shared:test": "cd shared && npm test", "lint": "npm run server:lint && npm run client:lint", "server:lint": "cd server && npm run lint", "client:lint": "cd client && npm run lint", "type-check": "npm run server:type-check && npm run client:type-check", "server:type-check": "cd server && npm run type-check", "client:type-check": "cd client && npm run type-check", "format": "prettier --write .", "format:check": "prettier --check .", "db:migrate": "cd server && npm run db:migrate", "db:seed": "cd server && npm run db:seed", "db:reset": "cd server && npm run db:reset", "docker:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:dev:down": "docker-compose -f docker-compose.dev.yml down", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "deploy": "./scripts/deploy.sh", "dev:env": "./scripts/dev.sh", "setup": "./setup.sh"}, "keywords": ["ai", "writing", "novel", "gemini", "ollama", "comfyui", "creative-writing"], "author": "AugmentWriter Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "vitest": "^1.0.0", "@vitest/coverage-v8": "^1.0.0", "@vitest/ui": "^1.0.0", "prettier": "^3.0.0"}, "workspaces": ["client", "server", "shared"], "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}