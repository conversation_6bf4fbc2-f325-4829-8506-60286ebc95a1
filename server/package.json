{"name": "augment-writer-server", "version": "1.0.0", "description": "Backend API for AugmentWriter", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts --fix", "type-check": "tsc --noEmit", "db:generate": "dotenv -e .env -- prisma generate", "db:migrate": "dotenv -e .env -- prisma migrate dev", "db:deploy": "dotenv -e .env -- prisma migrate deploy", "db:seed": "dotenv -e .env -- tsx prisma/seed.ts", "db:studio": "dotenv -e .env -- prisma studio", "db:reset": "dotenv -e .env -- prisma migrate reset"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "socket.io": "^4.7.4", "@prisma/client": "^5.6.0", "redis": "^4.6.10", "ioredis": "^5.3.2", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "axios": "^1.6.2", "@google/generative-ai": "^0.2.1", "ollama": "^0.4.8", "ws": "^8.14.2", "uuid": "^9.0.1", "zod": "^3.22.4", "winston": "^3.11.0", "dotenv": "^16.3.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@types/node": "^20.9.0", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "dotenv-cli": "^7.3.0", "eslint": "^8.53.0", "typescript": "^5.2.2", "tsx": "^4.1.4", "prisma": "^5.6.0", "vitest": "^0.34.6", "@vitest/coverage-v8": "^0.34.6", "supertest": "^6.3.3", "@types/supertest": "^2.0.16"}}