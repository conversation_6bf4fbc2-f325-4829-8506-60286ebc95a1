import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'
import { seedAIProviders } from './seedAIProviders.js'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 开始数据库种子数据初始化...')

  // 创建测试用户
  console.log('👤 创建测试用户...')
  
  const hashedPassword = await bcrypt.hash('password123', 12)
  
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'testuser',
      email: '<EMAIL>',
      password: hashedPassword,
      settings: {
        theme: 'light',
        language: 'zh-CN',
        autoSave: true,
        aiAssistance: true,
      },
    },
  })

  console.log(`✅ 创建测试用户: ${testUser.email}`)

  // 创建管理员用户
  console.log('👤 创建管理员用户...')

  const adminHashedPassword = await bcrypt.hash('a@1234567', 12)

  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      password: adminHashedPassword,
      settings: {
        theme: 'dark',
        language: 'zh-CN',
        autoSave: true,
        aiAssistance: true,
      },
    },
  })

  console.log(`✅ 创建管理员用户: ${adminUser.email}`)

  // 创建示例小说
  console.log('📚 创建示例小说...')
  
  const sampleNovel = await prisma.novel.upsert({
    where: { id: 'sample-novel-id' },
    update: {},
    create: {
      id: 'sample-novel-id',
      userId: testUser.id,
      title: '时间旅行者的日记',
      description: '一个关于时间旅行的科幻小说，讲述了一位年轻科学家意外发现时间机器后的冒险经历。',
      genre: '科幻',
      styleSample: `
        夜幕降临，实验室里只剩下微弱的蓝光在闪烁。艾莉克斯博士凝视着眼前这台奇异的装置，
        心中既兴奋又忐忑。经过三年的研究，她终于完成了这个被同事们称为"疯狂想法"的项目。
        
        "时间，真的可以被操控吗？"她轻声自语，手指轻抚过控制面板上冰冷的按钮。
        
        突然，机器发出了低沉的嗡鸣声，实验室里的空气似乎都开始颤抖...
      `,
      status: 'writing',
      wordCount: 15000,
      chapterCount: 3,
      settings: {
        targetWordCount: 80000,
        dailyGoal: 1000,
        autoBackup: true,
      },
    },
  })

  console.log(`✅ 创建示例小说: ${sampleNovel.title}`)

  // 创建示例章节
  console.log('📄 创建示例章节...')
  
  const chapters = [
    {
      title: '第一章：意外的发现',
      content: `
        艾莉克斯·陈博士从来没有想过，她的人生会在这个平凡的周二晚上发生翻天覆地的变化。
        
        作为麻省理工学院最年轻的物理学教授，她一直专注于理论物理的研究，特别是时间和空间的本质。
        同事们都认为她的研究过于超前，甚至有些不切实际，但艾莉克斯从未放弃过自己的理想。
        
        "时间不是线性的，"她经常这样告诉学生们，"它更像是一条可以弯曲的河流。"
        
        今晚，她终于要验证这个理论了。实验室里的时间扭曲装置已经准备就绪，所有的计算都已经完成。
        她深吸一口气，按下了启动按钮。
        
        瞬间，整个世界都变了...
      `,
      orderIndex: 0,
      status: 'completed' as const,
      wordCount: 180,
    },
    {
      title: '第二章：第一次跳跃',
      content: `
        当艾莉克斯睁开眼睛时，她发现自己站在一个完全陌生的地方。
        
        这里不是她熟悉的实验室，而是一个古老的图书馆。高耸的书架直达天花板，
        空气中弥漫着古老羊皮纸的味道。微弱的烛光在书架间跳跃，
        投下摇曳的影子。
        
        "这是哪里？"她困惑地环顾四周。
        
        突然，她注意到墙上的一个日历。上面显示的日期让她震惊不已：1692年。
        
        "不可能..."她喃喃自语，"我真的穿越时间了？"
        
        就在这时，图书馆的门被推开了，一个穿着古装的男子走了进来...
      `,
      orderIndex: 1,
      status: 'completed' as const,
      wordCount: 160,
    },
    {
      title: '第三章：历史的重量',
      content: `
        "您是谁？为什么会出现在这里？"那个男子用古老的英语问道。
        
        艾莉克斯意识到自己必须小心应对。在17世纪，一个突然出现的陌生女子
        很可能被当作女巫。她快速思考着如何解释自己的存在。
        
        "我...我是一个旅行者，"她小心翼翼地说，"迷路了。"
        
        男子仔细打量着她，显然对她的现代服装感到困惑。
        艾莉克斯这才意识到，她的牛仔裤和T恤在这个时代是多么突兀。
        
        "您的服装很...特别，"男子说道，"您来自哪里？"
        
        艾莉克斯知道，她必须找到回到现代的方法，但首先，
        她需要在这个危险的时代生存下去...
      `,
      orderIndex: 2,
      status: 'writing' as const,
      wordCount: 180,
    },
  ]

  for (const chapterData of chapters) {
    const chapter = await prisma.chapter.upsert({
      where: {
        novelId_orderIndex: {
          novelId: sampleNovel.id,
          orderIndex: chapterData.orderIndex,
        },
      },
      update: {},
      create: {
        novelId: sampleNovel.id,
        title: chapterData.title,
        content: chapterData.content.trim(),
        orderIndex: chapterData.orderIndex,
        status: chapterData.status,
        wordCount: chapterData.wordCount,
      },
    })
    
    console.log(`✅ 创建章节: ${chapter.title}`)
  }

  // 创建示例大纲
  console.log('📋 创建示例大纲...')
  
  const outlineStructure = {
    structure: [
      {
        id: 'act1',
        title: '第一幕：发现',
        description: '主角发现时间机器并进行第一次时间旅行',
        type: 'act',
        orderIndex: 0,
        status: 'writing',
        estimatedWordCount: 25000,
        children: [
          {
            id: 'ch1',
            title: '第一章：意外的发现',
            type: 'chapter',
            orderIndex: 0,
            status: 'completed',
            estimatedWordCount: 3000,
            actualWordCount: 180,
          },
          {
            id: 'ch2',
            title: '第二章：第一次跳跃',
            type: 'chapter',
            orderIndex: 1,
            status: 'completed',
            estimatedWordCount: 3000,
            actualWordCount: 160,
          },
          {
            id: 'ch3',
            title: '第三章：历史的重量',
            type: 'chapter',
            orderIndex: 2,
            status: 'writing',
            estimatedWordCount: 3000,
            actualWordCount: 180,
          },
        ],
      },
      {
        id: 'act2',
        title: '第二幕：冒险',
        description: '主角在不同时代的冒险经历和遇到的挑战',
        type: 'act',
        orderIndex: 1,
        status: 'planned',
        estimatedWordCount: 40000,
        children: [
          {
            id: 'ch4',
            title: '第四章：中世纪的秘密',
            type: 'chapter',
            orderIndex: 3,
            status: 'planned',
            estimatedWordCount: 4000,
          },
          {
            id: 'ch5',
            title: '第五章：未来的警告',
            type: 'chapter',
            orderIndex: 4,
            status: 'planned',
            estimatedWordCount: 4000,
          },
        ],
      },
      {
        id: 'act3',
        title: '第三幕：抉择',
        description: '主角面临重大抉择，决定时间线的命运',
        type: 'act',
        orderIndex: 2,
        status: 'planned',
        estimatedWordCount: 15000,
        children: [
          {
            id: 'ch6',
            title: '第六章：时间的代价',
            type: 'chapter',
            orderIndex: 5,
            status: 'planned',
            estimatedWordCount: 5000,
          },
        ],
      },
    ],
  }

  // 创建大纲（如果不存在）
  const existingOutline = await prisma.outline.findUnique({
    where: { novelId: sampleNovel.id },
  })

  if (!existingOutline) {
    await prisma.outline.create({
      data: {
        novelId: sampleNovel.id,
        structure: outlineStructure,
      },
    })
    console.log('✅ 示例大纲创建成功')
  } else {
    console.log('ℹ️  示例大纲已存在，跳过创建')
  }

  console.log(`✅ 创建示例大纲`)

  // 创建示例角色
  console.log('👥 创建示例角色...')
  
  const characters = [
    {
      name: '艾莉克斯·陈博士',
      description: '主角，年轻的物理学教授，时间机器的发明者',
      appearance: '亚裔女性，28岁，中等身材，黑色长发，通常穿着简单的休闲装',
      personality: '聪明、好奇、勇敢但有时冲动，对科学有着无限的热情',
      background: '麻省理工学院物理学博士，专攻理论物理，特别是时间和空间研究',
      role: 'protagonist' as const,
      relationships: [
        {
          characterId: 'char-mentor',
          relationship: '师生关系',
          description: '威尔逊教授是她的导师和精神支柱',
        },
      ],
    },
    {
      name: '威尔逊教授',
      description: '艾莉克斯的导师，资深物理学家',
      appearance: '60岁左右的白人男性，花白胡须，总是穿着老式的毛衣',
      personality: '睿智、耐心、有些保守，但对学生很关爱',
      background: '在MIT任教30年，是理论物理领域的权威',
      role: 'supporting' as const,
      relationships: [
        {
          characterId: 'char-protagonist',
          relationship: '师生关系',
          description: '艾莉克斯最信任的导师',
        },
      ],
    },
    {
      name: '神秘图书管理员',
      description: '17世纪的图书管理员，似乎知道一些关于时间的秘密',
      appearance: '中年男性，穿着17世纪的服装，眼神深邃',
      personality: '神秘、博学、似乎对时间旅行并不感到惊讶',
      background: '身份不明，管理着一个古老的图书馆',
      role: 'supporting' as const,
      relationships: [],
    },
  ]

  for (const characterData of characters) {
    const character = await prisma.character.upsert({
      where: {
        novelId_name: {
          novelId: sampleNovel.id,
          name: characterData.name,
        },
      },
      update: {},
      create: {
        novelId: sampleNovel.id,
        name: characterData.name,
        description: characterData.description,
        appearance: characterData.appearance,
        personality: characterData.personality,
        background: characterData.background,
        role: characterData.role,
        relationships: characterData.relationships,
      },
    })
    
    console.log(`✅ 创建角色: ${character.name}`)
  }

  // 创建世界设定
  console.log('🌍 创建世界设定...')
  
  const worldSettings = [
    {
      category: '科技设定',
      title: '时间机器原理',
      content: '基于量子纠缠和时空弯曲理论，通过高能粒子加速器产生时空裂缝，实现时间跳跃。',
    },
    {
      category: '规则设定',
      title: '时间旅行规则',
      content: '1. 每次跳跃消耗大量能量\n2. 无法改变已知的重大历史事件\n3. 在过去停留时间有限\n4. 频繁使用会对身体造成损害',
    },
    {
      category: '世界观',
      title: '多元时间线理论',
      content: '存在无数平行时间线，每个选择都会创造新的分支。时间旅行者可以在不同分支间跳跃。',
    },
  ]

  for (const settingData of worldSettings) {
    const setting = await prisma.worldSetting.upsert({
      where: {
        novelId_category_title: {
          novelId: sampleNovel.id,
          category: settingData.category,
          title: settingData.title,
        },
      },
      update: {},
      create: {
        novelId: sampleNovel.id,
        category: settingData.category,
        title: settingData.title,
        content: settingData.content,
      },
    })
    
    console.log(`✅ 创建世界设定: ${setting.category} - ${setting.title}`)
  }

  // 初始化 AI Providers
  await seedAIProviders()

  console.log('\n🎉 数据库种子数据初始化完成!')
  console.log('\n📊 创建的数据统计:')
  console.log(`👤 用户: 2`)
  console.log(`📚 小说: 1`)
  console.log(`📄 章节: ${chapters.length}`)
  console.log(`📋 大纲: 1`)
  console.log(`👥 角色: ${characters.length}`)
  console.log(`🌍 世界设定: ${worldSettings.length}`)

  console.log('\n🔑 测试账户信息:')
  console.log(`邮箱: <EMAIL>`)
  console.log(`密码: password123`)

  console.log('\n🔑 管理员账户信息:')
  console.log(`邮箱: <EMAIL>`)
  console.log(`密码: a@1234567`)
}

main()
  .catch((e) => {
    console.error('❌ 种子数据初始化失败:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
