import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 默认 AI Provider 配置
const defaultProviders = [
  {
    name: 'OpenAI',
    type: 'openai',
    baseUrl: 'https://api.openai.com/v1',
    models: [
      { id: 'gpt-4', name: 'GPT-4', type: 'text' },
      { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', type: 'text' },
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', type: 'text' },
      { id: 'dall-e-3', name: 'DALL-E 3', type: 'image' },
      { id: 'dall-e-2', name: 'DALL-E 2', type: 'image' }
    ],
    config: {
      timeout: 300000, // 5 分钟
      maxTokens: 4000,
      temperature: 0.7,
      supportedFeatures: ['text', 'image', 'chat']
    },
    isActive: true,
    isDefault: false
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    type: 'ollama',
    baseUrl: 'http://localhost:11434/v1',
    models: [
      { id: 'llama3.2', name: 'Llama 3.2', type: 'text' },
      { id: 'llama3.2:70b', name: 'Llama 3.2 70B', type: 'text' },
      { id: 'qwen2.5', name: 'Qwen 2.5', type: 'text' },
      { id: 'deepseek-coder', name: 'DeepSeek Coder', type: 'text' },
      { id: 'mistral', name: 'Mistral', type: 'text' }
    ],
    config: {
      timeout: 300000, // 5 分钟
      maxTokens: 8000,
      temperature: 0.8,
      supportedFeatures: ['text', 'chat'],
      requiresApiKey: false
    },
    isActive: true,
    isDefault: true // 设为默认
  },
  {
    name: 'Google Gemini',
    type: 'gemini',
    baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
    models: [
      { id: 'gemini-2.5-pro', name: 'Gemini 2.5 Pro', type: 'text' },
      { id: 'gemini-2.5-flash', name: 'Gemini 2.5 Flash', type: 'text' },
      { id: 'gemini-2.0-flash', name: 'Gemini 2.0 Flash', type: 'text' },
      { id: 'gemini-pro-vision', name: 'Gemini Pro Vision', type: 'multimodal' }
    ],
    config: {
      timeout: 300000, // 5 分钟
      maxTokens: 8000,
      temperature: 0.7,
      supportedFeatures: ['text', 'multimodal', 'chat']
    },
    isActive: true,
    isDefault: false
  },
  {
    name: 'Anthropic Claude',
    type: 'anthropic',
    baseUrl: 'https://api.anthropic.com/v1',
    models: [
      { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet', type: 'text' },
      { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', type: 'text' },
      { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku', type: 'text' }
    ],
    config: {
      timeout: 300000, // 5 分钟
      maxTokens: 4000,
      temperature: 0.7,
      supportedFeatures: ['text', 'chat']
    },
    isActive: true,
    isDefault: false
  },
  {
    name: 'Azure OpenAI',
    type: 'azure-openai',
    baseUrl: 'https://your-resource.openai.azure.com/openai/deployments',
    models: [
      { id: 'gpt-4', name: 'GPT-4 (Azure)', type: 'text' },
      { id: 'gpt-35-turbo', name: 'GPT-3.5 Turbo (Azure)', type: 'text' }
    ],
    config: {
      timeout: 300000, // 5 分钟
      maxTokens: 4000,
      temperature: 0.7,
      apiVersion: '2024-02-01',
      supportedFeatures: ['text', 'chat']
    },
    isActive: false, // 默认不激活，需要用户配置
    isDefault: false
  },
  {
    name: 'Cohere',
    type: 'cohere',
    baseUrl: 'https://api.cohere.ai/v1',
    models: [
      { id: 'command-r-plus', name: 'Command R+', type: 'text' },
      { id: 'command-r', name: 'Command R', type: 'text' },
      { id: 'command', name: 'Command', type: 'text' }
    ],
    config: {
      timeout: 300000, // 5 分钟
      maxTokens: 4000,
      temperature: 0.7,
      supportedFeatures: ['text', 'chat']
    },
    isActive: false,
    isDefault: false
  }
]

export async function seedAIProviders() {
  console.log('🤖 开始初始化 AI Providers...')

  for (const provider of defaultProviders) {
    try {
      const existingProvider = await prisma.aIProvider.findUnique({
        where: { name: provider.name }
      })

      if (!existingProvider) {
        await prisma.aIProvider.create({
          data: provider
        })
        console.log(`✅ 创建 AI Provider: ${provider.name}`)
      } else {
        console.log(`ℹ️  AI Provider 已存在: ${provider.name}`)
      }
    } catch (error) {
      console.error(`❌ 创建 AI Provider 失败: ${provider.name}`, error)
    }
  }

  console.log('🎉 AI Providers 初始化完成!')
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  seedAIProviders()
    .catch((e) => {
      console.error(e)
      process.exit(1)
    })
    .finally(async () => {
      await prisma.$disconnect()
    })
}

export default seedAIProviders
