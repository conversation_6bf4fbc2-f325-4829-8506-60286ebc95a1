// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// 用户表
// ============================================================================

model User {
  id        String   @id @default(uuid())
  username  String   @unique @db.VarChar(50)
  email     String   @unique @db.VarChar(255)
  password  String   @db.VarChar(255)
  avatar    String?  @db.VarChar(500)
  settings  Json?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  novels         Novel[]
  aiGenerations  AIGeneration[]
  
  @@map("users")
}

// ============================================================================
// 小说表
// ============================================================================

model Novel {
  id           String      @id @default(uuid())
  userId       String      @map("user_id")
  title        String      @db.VarChar(200)
  description  String?     @db.Text
  genre        String?     @db.VarChar(50)
  styleSample  String?     @map("style_sample") @db.Text
  coverImage   String?     @map("cover_image") @db.VarChar(500)
  status       NovelStatus @default(draft)
  wordCount    Int         @default(0) @map("word_count")
  chapterCount Int         @default(0) @map("chapter_count")
  settings     Json?
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")

  // 关联关系
  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  chapters      Chapter[]
  outlines      Outline[]
  characters    Character[]
  worldSettings WorldSetting[]
  illustrations Illustration[]
  aiGenerations AIGeneration[]

  @@map("novels")
}

enum NovelStatus {
  draft
  writing
  completed
  published
}

// ============================================================================
// 章节表
// ============================================================================

model Chapter {
  id         String        @id @default(uuid())
  novelId    String        @map("novel_id")
  title      String        @db.VarChar(200)
  content    String        @db.Text
  orderIndex Int           @map("order_index")
  status     ChapterStatus @default(draft)
  wordCount  Int           @default(0) @map("word_count")
  summary    String?       @db.Text
  createdAt  DateTime      @default(now()) @map("created_at")
  updatedAt  DateTime      @updatedAt @map("updated_at")

  // 关联关系
  novel         Novel          @relation(fields: [novelId], references: [id], onDelete: Cascade)
  illustrations Illustration[]

  @@unique([novelId, orderIndex])
  @@map("chapters")
}

enum ChapterStatus {
  draft
  writing
  completed
}

// ============================================================================
// 大纲表
// ============================================================================

model Outline {
  id        String   @id @default(uuid())
  novelId   String   @unique @map("novel_id") // 每个小说只能有一个大纲
  structure Json     // 存储大纲的树形结构
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  novel Novel @relation(fields: [novelId], references: [id], onDelete: Cascade)

  @@map("outlines")
}

// ============================================================================
// 角色表
// ============================================================================

model Character {
  id            String        @id @default(uuid())
  novelId       String        @map("novel_id")
  name          String        @db.VarChar(100)
  description   String?       @db.Text
  appearance    String?       @db.Text
  personality   String?       @db.Text
  background    String?       @db.Text
  role          CharacterRole @default(supporting)
  relationships Json?         // 存储角色关系数组
  avatar        String?       @db.VarChar(500)
  createdAt     DateTime      @default(now()) @map("created_at")
  updatedAt     DateTime      @updatedAt @map("updated_at")

  // 关联关系
  novel Novel @relation(fields: [novelId], references: [id], onDelete: Cascade)

  @@unique([novelId, name])
  @@map("characters")
}

enum CharacterRole {
  protagonist
  antagonist
  supporting
  minor
}

// ============================================================================
// 世界设定表
// ============================================================================

model WorldSetting {
  id        String   @id @default(uuid())
  novelId   String   @map("novel_id")
  category  String   @db.VarChar(100) // 如：地理、历史、魔法系统等
  title     String   @db.VarChar(200)
  content   String   @db.Text
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  novel Novel @relation(fields: [novelId], references: [id], onDelete: Cascade)

  @@unique([novelId, category, title])
  @@map("world_settings")
}

// ============================================================================
// AI生成历史表
// ============================================================================

model AIGeneration {
  id             String            @id @default(uuid())
  userId         String            @map("user_id")
  novelId        String?           @map("novel_id")
  type           AIGenerationType
  prompt         String            @db.Text
  result         String            @db.Text
  model          String            @db.VarChar(100)
  modelProvider  String            @map("model_provider") @db.VarChar(50)
  parameters     Json?
  usage          Json?             // token使用情况
  status         GenerationStatus  @default(pending)
  errorMessage   String?           @map("error_message") @db.Text
  createdAt      DateTime          @default(now()) @map("created_at")

  // 关联关系
  user  User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  novel Novel? @relation(fields: [novelId], references: [id], onDelete: Cascade)

  @@map("ai_generations")
}

// ============================================================================
// AI服务提供商表
// ============================================================================

model AIProvider {
  id          String   @id @default(uuid())
  name        String   @unique @db.VarChar(100)
  type        String   @db.VarChar(50) // 'openai', 'ollama', 'gemini', 'custom'
  baseUrl     String   @map("base_url") @db.VarChar(500)
  apiKey      String?  @map("api_key") @db.VarChar(500)
  models      Json     // 支持的模型列表 [{"id": "gpt-4", "name": "GPT-4"}]
  config      Json?    // 额外配置参数 {"timeout": 30, "maxTokens": 4000}
  isActive    Boolean  @default(true) @map("is_active")
  isDefault   Boolean  @default(false) @map("is_default")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("ai_providers")
}

enum AIGenerationType {
  continue
  rewrite
  outline
  character
  summary
  image
}

enum GenerationStatus {
  pending
  processing
  completed
  failed
}

// ============================================================================
// 插图表
// ============================================================================

model Illustration {
  id           String   @id @default(uuid())
  novelId      String   @map("novel_id")
  chapterId    String?  @map("chapter_id")
  title        String?  @db.VarChar(200)
  description  String   @db.Text
  imageUrl     String   @map("image_url") @db.VarChar(500)
  thumbnailUrl String?  @map("thumbnail_url") @db.VarChar(500)
  prompt       String   @db.Text
  model        String   @db.VarChar(100)
  parameters   Json?
  createdAt    DateTime @default(now()) @map("created_at")

  // 关联关系
  novel   Novel    @relation(fields: [novelId], references: [id], onDelete: Cascade)
  chapter Chapter? @relation(fields: [chapterId], references: [id], onDelete: SetNull)

  @@map("illustrations")
}
