import jwt from 'jsonwebtoken'

/**
 * 生成测试用的 JWT token
 */
export function generateTestToken(userId: string): string {
  const secret = process.env.JWT_SECRET || 'test-secret'
  return jwt.sign(
    { 
      userId,
      type: 'access'
    },
    secret,
    { expiresIn: '1h' }
  )
}

/**
 * 解析测试 token
 */
export function parseTestToken(token: string): { userId: string } {
  const secret = process.env.JWT_SECRET || 'test-secret'
  const decoded = jwt.verify(token, secret) as any
  return { userId: decoded.userId }
}

/**
 * 创建测试用的错误对象
 */
export function createTestError(message: string, code?: string): Error {
  const error = new Error(message)
  if (code) {
    (error as any).code = code
  }
  return error
}

/**
 * 模拟 AI 服务错误
 */
export const mockAIErrors = {
  providerUnavailable: (provider: string, reason: string) => 
    new Error(`AI Provider "${provider}" is not available: ${reason}`),
  
  modelUnavailable: (model: string, reason: string) => 
    new Error(`Model "${model}" is not available or has reached rate limit`),
  
  requestTimeout: (timeout: string) => 
    new Error(`Request timeout: AI service did not respond within ${timeout}`),
  
  quotaExceeded: (quotaType: string) => 
    new Error(`Quota exceeded: You have reached your ${quotaType} AI generation limit`),
  
  contentFiltered: (reason: string) => 
    new Error(`Content filtered: The generated content violates content policy`),
  
  unknownError: (message: string) => 
    new Error(message)
}
