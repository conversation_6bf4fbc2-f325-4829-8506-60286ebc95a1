import type { ApiResponse } from '@shared/index.js'

/**
 * AI 错误类型枚举
 */
export enum AIErrorType {
  PROVIDER_UNAVAILABLE = 'AI_PROVIDER_UNAVAILABLE',
  MODEL_UNAVAILABLE = 'AI_MODEL_UNAVAILABLE',
  REQUEST_TIMEOUT = 'AI_REQUEST_TIMEOUT',
  QUOTA_EXCEEDED = 'AI_QUOTA_EXCEEDED',
  CONTENT_FILTERED = 'AI_CONTENT_FILTERED',
  INVALID_PARAMETERS = 'AI_INVALID_PARAMETERS',
  NETWORK_ERROR = 'AI_NETWORK_ERROR',
  GENERATION_FAILED = 'AI_GENERATION_FAILED',
}

/**
 * AI 错误详情接口
 */
export interface AIErrorDetails {
  provider?: string
  model?: string
  reason?: string
  timeout?: string
  quotaType?: string
  resetTime?: string
  suggestion?: string
  originalError?: string
  parameters?: Record<string, any>
}

/**
 * AI 错误信息
 */
export interface AIErrorInfo {
  code: AIErrorType
  message: string
  statusCode: number
  details: AIErrorDetails
}

/**
 * 分析错误并返回结构化的错误信息
 */
export function analyzeAIError(error: Error): AIErrorInfo {
  const errorMessage = error.message.toLowerCase()
  
  // Provider 不可用错误
  if (errorMessage.includes('provider') && (errorMessage.includes('not available') || errorMessage.includes('unavailable'))) {
    const providerMatch = error.message.match(/provider\s+"([^"]+)"/i)
    const reasonMatch = error.message.match(/:\s*(.+)$/i)
    
    return {
      code: AIErrorType.PROVIDER_UNAVAILABLE,
      message: error.message,
      statusCode: 503,
      details: {
        provider: providerMatch?.[1] || 'unknown',
        reason: reasonMatch?.[1] || 'Provider service unavailable',
        suggestion: 'Please check provider configuration or try again later',
      }
    }
  }
  
  // 模型不可用错误
  if (errorMessage.includes('model') && (errorMessage.includes('not available') || errorMessage.includes('unavailable') || errorMessage.includes('rate limit'))) {
    const modelMatch = error.message.match(/model\s+"([^"]+)"/i)
    
    return {
      code: AIErrorType.MODEL_UNAVAILABLE,
      message: error.message,
      statusCode: 503,
      details: {
        model: modelMatch?.[1] || 'unknown',
        reason: errorMessage.includes('rate limit') ? 'Rate limit exceeded' : 'Model unavailable',
        suggestion: 'Please try a different model or wait before retrying',
      }
    }
  }
  
  // 请求超时错误
  if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
    const timeoutMatch = error.message.match(/(\d+\s*(?:seconds?|minutes?))/i)
    
    return {
      code: AIErrorType.REQUEST_TIMEOUT,
      message: error.message,
      statusCode: 408,
      details: {
        timeout: timeoutMatch?.[1] || '30 seconds',
        suggestion: 'Please try again or reduce the content length',
      }
    }
  }
  
  // 配额超限错误
  if (errorMessage.includes('quota') || errorMessage.includes('limit exceeded') || errorMessage.includes('usage limit')) {
    const quotaMatch = error.message.match(/(daily|monthly|hourly)/i)
    const resetTime = new Date()
    
    // 根据配额类型计算重置时间
    if (quotaMatch?.[1]?.toLowerCase() === 'daily') {
      resetTime.setDate(resetTime.getDate() + 1)
      resetTime.setHours(0, 0, 0, 0)
    } else if (quotaMatch?.[1]?.toLowerCase() === 'monthly') {
      resetTime.setMonth(resetTime.getMonth() + 1)
      resetTime.setDate(1)
      resetTime.setHours(0, 0, 0, 0)
    } else {
      resetTime.setHours(resetTime.getHours() + 1)
      resetTime.setMinutes(0, 0, 0)
    }
    
    return {
      code: AIErrorType.QUOTA_EXCEEDED,
      message: error.message,
      statusCode: 429,
      details: {
        quotaType: quotaMatch?.[1]?.toLowerCase() || 'daily',
        resetTime: resetTime.toISOString(),
        suggestion: 'Please try again later or upgrade your plan',
      }
    }
  }
  
  // 内容过滤错误
  if (errorMessage.includes('content filtered') || errorMessage.includes('content policy') || errorMessage.includes('inappropriate content')) {
    return {
      code: AIErrorType.CONTENT_FILTERED,
      message: error.message,
      statusCode: 400,
      details: {
        reason: 'Content policy violation',
        suggestion: 'Please modify your prompt or previous content',
      }
    }
  }
  
  // 参数错误
  if (errorMessage.includes('invalid parameter') || errorMessage.includes('parameter') || errorMessage.includes('validation')) {
    return {
      code: AIErrorType.INVALID_PARAMETERS,
      message: error.message,
      statusCode: 400,
      details: {
        reason: 'Invalid request parameters',
        suggestion: 'Please check your request parameters and try again',
      }
    }
  }
  
  // 网络错误
  if (errorMessage.includes('network') || errorMessage.includes('connection') || errorMessage.includes('econnrefused') || errorMessage.includes('fetch')) {
    return {
      code: AIErrorType.NETWORK_ERROR,
      message: error.message,
      statusCode: 502,
      details: {
        reason: 'Network connection failed',
        suggestion: 'Please check your internet connection and try again',
      }
    }
  }
  
  // 通用生成失败错误
  return {
    code: AIErrorType.GENERATION_FAILED,
    message: error.message,
    statusCode: 500,
    details: {
      originalError: error.message,
      suggestion: 'Please try again later or contact support if the problem persists',
    }
  }
}

/**
 * 创建 AI 错误响应
 */
export function createAIErrorResponse(error: Error): { response: ApiResponse, statusCode: number } {
  const errorInfo = analyzeAIError(error)
  
  const response: ApiResponse = {
    success: false,
    error: {
      code: errorInfo.code,
      message: errorInfo.message,
      details: errorInfo.details,
    }
  }
  
  return {
    response,
    statusCode: errorInfo.statusCode
  }
}

/**
 * 记录 AI 错误日志
 */
export function logAIError(error: Error, context?: Record<string, any>) {
  const errorInfo = analyzeAIError(error)
  
  console.error('AI Error:', {
    type: errorInfo.code,
    message: errorInfo.message,
    statusCode: errorInfo.statusCode,
    details: errorInfo.details,
    context,
    stack: error.stack,
    timestamp: new Date().toISOString(),
  })
}
