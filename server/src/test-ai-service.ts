/**
 * AI服务测试脚本
 * 用于验证AI集成层的基本功能
 */

import { AIService } from './services/ai/ai-service.js'
import type { AIGenerationRequest } from '@shared/index.js'

async function testAIService() {
  console.log('🤖 开始测试AI服务...\n')

  try {
    // 创建AI服务实例
    const aiService = new AIService()
    
    // 检查提供者状态
    console.log('📋 检查AI提供者状态:')
    const providersStatus = await aiService.getProvidersStatus()
    providersStatus.forEach(status => {
      console.log(`  - ${status.provider}: ${status.available ? '✅ 可用' : '❌ 不可用'}`)
      console.log(`    模型: ${status.model.model}`)
      console.log(`    能力: ${status.model.capabilities.join(', ')}`)
    })
    console.log()

    // 测试文本生成
    console.log('📝 测试文本生成:')
    const generateRequest: AIGenerationRequest = {
      type: 'continue',
      prompt: '从前有一个勇敢的骑士，他住在一座古老的城堡里...',
      context: '这是一个中世纪奇幻故事的开头',
    }

    try {
      const generateResult = await aiService.generateText(generateRequest)
      console.log('✅ 文本生成成功')
      console.log(`  使用模型: ${generateResult.model.provider}/${generateResult.model.model}`)
      console.log(`  生成内容: ${generateResult.result.substring(0, 100)}...`)
      if (generateResult.usage) {
        console.log(`  Token使用: ${generateResult.usage.totalTokens}`)
      }
    } catch (error) {
      console.log('❌ 文本生成失败:', error.message)
    }
    console.log()

    // 测试风格分析
    console.log('🎨 测试风格分析:')
    const sampleText = `
      夜幕降临，古老的城堡在月光下显得格外神秘。石墙上爬满了常春藤，
      微风轻抚过叶片，发出沙沙的声响。远处传来猫头鹰的叫声，
      为这宁静的夜晚增添了几分诗意。
    `

    try {
      const styleAnalysis = await aiService.analyzeStyle(sampleText)
      console.log('✅ 风格分析成功')
      console.log(`  语调: ${styleAnalysis.tone}`)
      console.log(`  风格: ${styleAnalysis.style}`)
      console.log(`  词汇水平: ${styleAnalysis.vocabulary}`)
      console.log(`  主题: ${styleAnalysis.themes.join(', ')}`)
    } catch (error) {
      console.log('❌ 风格分析失败:', error.message)
    }
    console.log()

    // 测试大纲生成
    console.log('📋 测试大纲生成:')
    const synopsis = '一个年轻的法师发现了一个古老的魔法书，书中记载着失传已久的时间魔法。但是使用这种魔法需要付出巨大的代价...'

    try {
      const outline = await aiService.generateOutline(synopsis)
      console.log('✅ 大纲生成成功')
      console.log(`  结构层数: ${outline.structure.length}`)
      outline.structure.forEach((act, index) => {
        console.log(`  ${index + 1}. ${act.title} (${act.type})`)
        if (act.children) {
          act.children.forEach((chapter, chIndex) => {
            console.log(`     ${chIndex + 1}. ${chapter.title}`)
          })
        }
      })
    } catch (error) {
      console.log('❌ 大纲生成失败:', error.message)
    }
    console.log()

    // 测试文本总结
    console.log('📄 测试文本总结:')
    const longText = `
      艾莉亚是一个十六岁的少女，生活在边境小镇维斯特洛。她的父亲是镇上的铁匠，
      母亲在她很小的时候就去世了。艾莉亚从小就展现出对魔法的天赋，但在这个
      魔法被禁止的王国里，她只能偷偷练习。
      
      一天，一个神秘的旅行者来到了小镇，他给了艾莉亚一本古老的魔法书。
      书中记载着强大的时间魔法，但警告说使用这种魔法会缩短施法者的寿命。
      
      当王国的军队来到小镇搜查魔法师时，艾莉亚不得不做出选择：是逃跑保命，
      还是使用时间魔法保护她的家人和朋友？
    `

    try {
      const summary = await aiService.summarizeText(longText)
      console.log('✅ 文本总结成功')
      console.log(`  总结: ${summary.summary}`)
      console.log(`  关键点: ${summary.keyPoints.join(', ')}`)
      console.log(`  角色: ${summary.characters.join(', ')}`)
      console.log(`  未解决线索: ${summary.unresolved.join(', ')}`)
    } catch (error) {
      console.log('❌ 文本总结失败:', error.message)
    }
    console.log()

    // 测试智能续写
    console.log('🧠 测试智能续写:')
    const currentContent = '艾莉亚握紧了手中的魔法书，她知道时间不多了...'
    const context = {
      characterProfiles: {
        '艾莉亚': {
          age: 16,
          occupation: '铁匠的女儿',
          abilities: ['时间魔法天赋'],
          personality: '勇敢、善良、有责任感'
        }
      },
      worldSettings: {
        setting: '中世纪奇幻世界',
        magic_status: '被禁止',
        current_threat: '王国军队搜查魔法师'
      }
    }

    try {
      const smartResult = await aiService.smartContinue(currentContent, context)
      console.log('✅ 智能续写成功')
      console.log(`  续写内容: ${smartResult.result.substring(0, 150)}...`)
    } catch (error) {
      console.log('❌ 智能续写失败:', error.message)
    }
    console.log()

    console.log('🎉 AI服务测试完成!')

  } catch (error) {
    console.error('❌ AI服务初始化失败:', error.message)
    console.error('请检查环境配置和API密钥设置')
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testAIService().catch(console.error)
}

export { testAIService }
