import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import compression from 'compression'
import rateLimit from 'express-rate-limit'
import { createServer } from 'http'
import { Server as SocketIOServer } from 'socket.io'

import { errorHandler, notFoundHandler } from './middleware/error-handler.js'
import { authRoutes } from './routes/auth.js'
import { novelRoutes } from './routes/novels.js'
import { chapterRoutes } from './routes/chapters.js'
import { aiRoutes } from './routes/ai.js'
import { aiProviderRoutes } from './routes/ai-providers.js'
import { userRoutes } from './routes/users.js'

export function createApp() {
  const app = express()
  const server = createServer(app)
  
  // Socket.IO配置
  const io = new SocketIOServer(server, {
    cors: {
      origin: process.env.CORS_ORIGIN || 'http://localhost:8181',
      methods: ['GET', 'POST'],
    },
  })

  // 基础中间件
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
  }))

  app.use(cors({
    origin: process.env.CORS_ORIGIN || 'http://localhost:8181',
    credentials: true,
  }))

  app.use(compression())
  app.use(morgan('combined'))
  app.use(express.json({ limit: '10mb' }))
  app.use(express.urlencoded({ extended: true, limit: '10mb' }))

  // 速率限制
  const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '15') * 60 * 1000, // 15分钟
    max: parseInt(process.env.RATE_LIMIT_MAX || '100'), // 限制每个IP 100个请求
    message: {
      error: 'Too many requests from this IP, please try again later.',
    },
    standardHeaders: true,
    legacyHeaders: false,
  })
  app.use('/api/', limiter)

  // 健康检查
  app.get('/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: process.env.APP_VERSION || '1.0.0',
    })
  })

  // API路由
  app.use('/api/auth', authRoutes)
  app.use('/api/users', userRoutes)
  app.use('/api/novels', novelRoutes)
  app.use('/api/chapters', chapterRoutes)
  app.use('/api/ai', aiRoutes)
app.use('/api/ai-providers', aiProviderRoutes)

  // Socket.IO事件处理
  io.on('connection', (socket) => {
    console.log('用户连接:', socket.id)

    socket.on('join_novel', (novelId: string) => {
      socket.join(`novel_${novelId}`)
      console.log(`用户 ${socket.id} 加入小说 ${novelId}`)
    })

    socket.on('leave_novel', (novelId: string) => {
      socket.leave(`novel_${novelId}`)
      console.log(`用户 ${socket.id} 离开小说 ${novelId}`)
    })

    socket.on('content_change', (data) => {
      socket.to(`novel_${data.novelId}`).emit('content_change', data)
    })

    socket.on('cursor_change', (data) => {
      socket.to(`novel_${data.novelId}`).emit('cursor_change', data)
    })

    socket.on('disconnect', () => {
      console.log('用户断开连接:', socket.id)
    })
  })

  // 错误处理中间件
  app.use(notFoundHandler)
  app.use(errorHandler)

  return { app, server, io }
}

export type AppInstance = ReturnType<typeof createApp>
