import type { Request, Response, NextFunction } from 'express'
import { ZodError } from 'zod'
import type { ApiResponse } from '@shared/index.js'

// 自定义应用错误类
export class AppError extends Error {
  public readonly statusCode: number
  public readonly code: string
  public readonly isOperational: boolean

  constructor(message: string, statusCode: number = 500, code: string = 'INTERNAL_ERROR') {
    super(message)
    this.statusCode = statusCode
    this.code = code
    this.isOperational = true

    Error.captureStackTrace(this, this.constructor)
  }
}

// 错误处理中间件
export function errorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  let statusCode = 500
  let errorCode = 'INTERNAL_ERROR'
  let message = '内部服务器错误'
  let details: any = undefined

  // 处理自定义应用错误
  if (error instanceof AppError) {
    statusCode = error.statusCode
    errorCode = error.code
    message = error.message
  }
  // 处理Zod验证错误
  else if (error instanceof ZodError) {
    statusCode = 400
    errorCode = 'VALIDATION_ERROR'
    message = '输入验证失败'
    details = error.issues.reduce((acc, issue) => {
      const path = issue.path.join('.')
      acc[path] = issue.message
      return acc
    }, {} as Record<string, string>)
  }
  // 处理Prisma错误
  else if (error.name === 'PrismaClientKnownRequestError') {
    const prismaError = error as any
    switch (prismaError.code) {
      case 'P2002': // 唯一约束违反
        statusCode = 409
        errorCode = 'ALREADY_EXISTS'
        const target = prismaError.meta?.target?.[0]
        message = target === 'email' ? '邮箱已被使用' : 
                 target === 'username' ? '用户名已被使用' : '数据已存在'
        break
      case 'P2025': // 记录未找到
        statusCode = 404
        errorCode = 'NOT_FOUND'
        message = '记录未找到'
        break
      default:
        statusCode = 500
        errorCode = 'DATABASE_ERROR'
        message = '数据库操作失败'
    }
  }
  // 处理JWT错误
  else if (error.name === 'TokenExpiredError') {
    statusCode = 401
    errorCode = 'TOKEN_EXPIRED'
    message = '令牌已过期'
  }
  else if (error.name === 'JsonWebTokenError') {
    statusCode = 401
    errorCode = 'UNAUTHORIZED'
    message = '无效的令牌'
  }
  // 处理其他已知错误
  else {
    // 在开发环境中显示原始错误信息
    if (process.env.NODE_ENV === 'development') {
      message = error.message
    }
  }

  // 构建错误响应
  const errorResponse: ApiResponse = {
    success: false,
    error: {
      code: errorCode,
      message,
      ...(details && { details }),
      ...(process.env.NODE_ENV === 'development' && { stack: error.stack }),
    },
  }

  // 记录错误日志
  console.error('Error:', {
    message: error.message,
    stack: error.stack,
    statusCode,
    errorCode,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  })

  res.status(statusCode).json(errorResponse)
}

// 404处理中间件
export function notFoundHandler(req: Request, res: Response): void {
  const errorResponse: ApiResponse = {
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `路由 ${req.method} ${req.path} 未找到`,
    },
  }

  res.status(404).json(errorResponse)
}

// 异步错误包装器
export function asyncHandler<T extends Request, U extends Response>(
  fn: (req: T, res: U, next: NextFunction) => Promise<any>
) {
  return (req: T, res: U, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

// 常用错误创建函数
export const createError = {
  badRequest: (message: string = '请求参数错误') => 
    new AppError(message, 400, 'BAD_REQUEST'),
  
  unauthorized: (message: string = '未授权访问') => 
    new AppError(message, 401, 'UNAUTHORIZED'),
  
  forbidden: (message: string = '禁止访问') => 
    new AppError(message, 403, 'FORBIDDEN'),
  
  notFound: (message: string = '资源未找到') => 
    new AppError(message, 404, 'NOT_FOUND'),
  
  conflict: (message: string = '资源冲突') => 
    new AppError(message, 409, 'CONFLICT'),
  
  tooManyRequests: (message: string = '请求过于频繁') => 
    new AppError(message, 429, 'TOO_MANY_REQUESTS'),
  
  internal: (message: string = '内部服务器错误') => 
    new AppError(message, 500, 'INTERNAL_ERROR'),
  
  serviceUnavailable: (message: string = '服务不可用') => 
    new AppError(message, 503, 'SERVICE_UNAVAILABLE'),
}
