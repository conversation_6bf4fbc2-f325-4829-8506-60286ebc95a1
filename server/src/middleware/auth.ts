import type { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import type { ApiResponse } from '@shared/index.js'

// 扩展Request接口以包含用户信息
export interface AuthenticatedRequest extends Request {
  user?: {
    userId: string
    email: string
    iat: number
    exp: number
  }
}

// JWT令牌验证中间件
export function authenticateToken(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void {
  const authHeader = req.headers.authorization
  
  // 检查Authorization头是否存在
  if (!authHeader) {
    const errorResponse: ApiResponse = {
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: '缺少访问令牌',
      },
    }
    res.status(401).json(errorResponse)
    return
  }

  // 检查Bearer格式
  const parts = authHeader.split(' ')
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    const errorResponse: ApiResponse = {
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: '令牌格式错误',
      },
    }
    res.status(401).json(errorResponse)
    return
  }

  const token = parts[1]
  const jwtSecret = process.env.JWT_SECRET

  // 检查JWT密钥是否配置
  if (!jwtSecret) {
    const errorResponse: ApiResponse = {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'JWT密钥未配置',
      },
    }
    res.status(500).json(errorResponse)
    return
  }

  try {
    // 验证JWT令牌
    const decoded = jwt.verify(token, jwtSecret) as any
    
    // 验证令牌包含必要的用户信息
    if (!decoded.userId || !decoded.email) {
      const errorResponse: ApiResponse = {
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '令牌信息不完整',
        },
      }
      res.status(401).json(errorResponse)
      return
    }

    // 将用户信息添加到请求对象
    req.user = {
      userId: decoded.userId,
      email: decoded.email,
      iat: decoded.iat,
      exp: decoded.exp,
    }

    next()
  } catch (error: any) {
    let errorResponse: ApiResponse

    if (error.name === 'TokenExpiredError') {
      errorResponse = {
        success: false,
        error: {
          code: 'TOKEN_EXPIRED',
          message: '访问令牌已过期',
        },
      }
    } else if (error.name === 'JsonWebTokenError') {
      errorResponse = {
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '令牌格式错误',
        },
      }
    } else {
      errorResponse = {
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '无效的访问令牌',
        },
      }
    }

    res.status(401).json(errorResponse)
  }
}

// 可选的认证中间件（不强制要求登录）
export function optionalAuth(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void {
  const authHeader = req.headers.authorization

  if (!authHeader) {
    next()
    return
  }

  // 如果有Authorization头，尝试验证
  authenticateToken(req, res, (error) => {
    if (error) {
      // 如果验证失败，清除用户信息但继续处理请求
      req.user = undefined
    }
    next()
  })
}

// JWT工具函数
export const jwtUtils = {
  /**
   * 生成访问令牌
   */
  generateAccessToken(payload: { userId: string; email: string }): string {
    const jwtSecret = process.env.JWT_SECRET
    if (!jwtSecret) {
      throw new Error('JWT密钥未配置')
    }

    const expiresIn = process.env.JWT_EXPIRES_IN || '7d'
    
    return jwt.sign(payload, jwtSecret, { expiresIn })
  },

  /**
   * 生成刷新令牌
   */
  generateRefreshToken(payload: { userId: string }): string {
    const jwtSecret = process.env.JWT_SECRET
    if (!jwtSecret) {
      throw new Error('JWT密钥未配置')
    }

    return jwt.sign(payload, jwtSecret, { expiresIn: '30d' })
  },

  /**
   * 验证令牌
   */
  verifyToken(token: string): any {
    const jwtSecret = process.env.JWT_SECRET
    if (!jwtSecret) {
      throw new Error('JWT密钥未配置')
    }

    return jwt.verify(token, jwtSecret)
  },

  /**
   * 解码令牌（不验证）
   */
  decodeToken(token: string): any {
    return jwt.decode(token)
  },
}
