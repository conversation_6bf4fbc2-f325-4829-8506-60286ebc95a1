import { describe, it, expect, vi, beforeEach } from 'vitest'
import { Request, Response, NextFunction } from 'express'
import { errorHandler, AppError } from './error-handler.js'

describe('错误处理中间件', () => {
  let mockReq: Partial<Request>
  let mockRes: Partial<Response>
  let mockNext: NextFunction
  let jsonSpy: any
  let statusSpy: any

  beforeEach(() => {
    jsonSpy = vi.fn()
    statusSpy = vi.fn().mockReturnValue({ json: jsonSpy })
    
    mockReq = {}
    mockRes = {
      status: statusSpy,
      json: jsonSpy,
    }
    mockNext = vi.fn()
  })

  describe('AppError处理', () => {
    it('应该正确处理自定义应用错误', () => {
      const error = new AppError('测试错误', 400, 'VALIDATION_ERROR')
      
      errorHandler(error, mockReq as Request, mockRes as Response, mockNext)
      
      expect(statusSpy).toHaveBeenCalledWith(400)
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '测试错误',
        },
      })
    })

    it('应该在开发环境中包含错误堆栈', () => {
      process.env.NODE_ENV = 'development'
      const error = new AppError('测试错误', 400, 'TEST_ERROR')
      
      errorHandler(error, mockReq as Request, mockRes as Response, mockNext)
      
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'TEST_ERROR',
          message: '测试错误',
          stack: error.stack,
        },
      })
    })

    it('应该在生产环境中隐藏错误堆栈', () => {
      process.env.NODE_ENV = 'production'
      const error = new AppError('测试错误', 400, 'TEST_ERROR')
      
      errorHandler(error, mockReq as Request, mockRes as Response, mockNext)
      
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'TEST_ERROR',
          message: '测试错误',
        },
      })
    })
  })

  describe('普通错误处理', () => {
    it('应该将普通错误转换为500状态码', () => {
      const error = new Error('普通错误')
      
      errorHandler(error, mockReq as Request, mockRes as Response, mockNext)
      
      expect(statusSpy).toHaveBeenCalledWith(500)
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '内部服务器错误',
        },
      })
    })

    it('应该在开发环境中显示原始错误信息', () => {
      process.env.NODE_ENV = 'development'
      const error = new Error('详细错误信息')
      
      errorHandler(error, mockReq as Request, mockRes as Response, mockNext)
      
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '详细错误信息',
          stack: error.stack,
        },
      })
    })
  })

  describe('验证错误处理', () => {
    it('应该处理Zod验证错误', () => {
      const zodError = {
        name: 'ZodError',
        issues: [
          {
            path: ['email'],
            message: '邮箱格式不正确',
          },
          {
            path: ['password'],
            message: '密码长度至少8位',
          },
        ],
      }
      
      errorHandler(zodError as any, mockReq as Request, mockRes as Response, mockNext)
      
      expect(statusSpy).toHaveBeenCalledWith(400)
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '输入验证失败',
          details: {
            email: '邮箱格式不正确',
            password: '密码长度至少8位',
          },
        },
      })
    })
  })

  describe('Prisma错误处理', () => {
    it('应该处理唯一约束违反错误', () => {
      const prismaError = {
        code: 'P2002',
        meta: {
          target: ['email'],
        },
      }
      
      errorHandler(prismaError as any, mockReq as Request, mockRes as Response, mockNext)
      
      expect(statusSpy).toHaveBeenCalledWith(409)
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'ALREADY_EXISTS',
          message: '邮箱已被使用',
        },
      })
    })

    it('应该处理记录未找到错误', () => {
      const prismaError = {
        code: 'P2025',
      }
      
      errorHandler(prismaError as any, mockReq as Request, mockRes as Response, mockNext)
      
      expect(statusSpy).toHaveBeenCalledWith(404)
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: '记录未找到',
        },
      })
    })
  })

  describe('JWT错误处理', () => {
    it('应该处理JWT过期错误', () => {
      const jwtError = {
        name: 'TokenExpiredError',
      }
      
      errorHandler(jwtError as any, mockReq as Request, mockRes as Response, mockNext)
      
      expect(statusSpy).toHaveBeenCalledWith(401)
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'TOKEN_EXPIRED',
          message: '令牌已过期',
        },
      })
    })

    it('应该处理JWT无效错误', () => {
      const jwtError = {
        name: 'JsonWebTokenError',
      }
      
      errorHandler(jwtError as any, mockReq as Request, mockRes as Response, mockNext)
      
      expect(statusSpy).toHaveBeenCalledWith(401)
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '无效的令牌',
        },
      })
    })
  })
})
