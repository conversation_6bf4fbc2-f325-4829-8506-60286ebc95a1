import { describe, it, expect, vi, beforeEach } from 'vitest'
import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import { authenticateToken } from './auth.js'
import type { AuthenticatedRequest } from './auth.js'

// Mock jwt
vi.mock('jsonwebtoken')

describe('认证中间件', () => {
  let mockReq: Partial<AuthenticatedRequest>
  let mockRes: Partial<Response>
  let mockNext: NextFunction
  let statusSpy: any
  let jsonSpy: any

  beforeEach(() => {
    vi.clearAllMocks()
    
    jsonSpy = vi.fn()
    statusSpy = vi.fn().mockReturnValue({ json: jsonSpy })
    
    mockReq = {
      headers: {},
    }
    mockRes = {
      status: statusSpy,
      json: jsonSpy,
    }
    mockNext = vi.fn()

    // Mock environment variable
    process.env.JWT_SECRET = 'test-secret'
  })

  describe('authenticateToken', () => {
    it('应该成功验证有效的Bearer token', () => {
      const mockPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
        iat: Date.now() / 1000,
        exp: Date.now() / 1000 + 3600,
      }

      mockReq.headers = {
        authorization: 'Bearer valid-token',
      }

      vi.mocked(jwt.verify).mockReturnValue(mockPayload)

      authenticateToken(mockReq as AuthenticatedRequest, mockRes as Response, mockNext)

      expect(jwt.verify).toHaveBeenCalledWith('valid-token', 'test-secret')
      expect(mockReq.user).toEqual(mockPayload)
      expect(mockNext).toHaveBeenCalled()
    })

    it('应该拒绝没有Authorization头的请求', () => {
      mockReq.headers = {}

      authenticateToken(mockReq as AuthenticatedRequest, mockRes as Response, mockNext)

      expect(statusSpy).toHaveBeenCalledWith(401)
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '缺少访问令牌',
        },
      })
      expect(mockNext).not.toHaveBeenCalled()
    })

    it('应该拒绝格式错误的Authorization头', () => {
      mockReq.headers = {
        authorization: 'InvalidFormat token',
      }

      authenticateToken(mockReq as AuthenticatedRequest, mockRes as Response, mockNext)

      expect(statusSpy).toHaveBeenCalledWith(401)
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '令牌格式错误',
        },
      })
      expect(mockNext).not.toHaveBeenCalled()
    })

    it('应该拒绝无效的token', () => {
      mockReq.headers = {
        authorization: 'Bearer invalid-token',
      }

      vi.mocked(jwt.verify).mockImplementation(() => {
        throw new Error('Invalid token')
      })

      authenticateToken(mockReq as AuthenticatedRequest, mockRes as Response, mockNext)

      expect(statusSpy).toHaveBeenCalledWith(401)
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '无效的访问令牌',
        },
      })
      expect(mockNext).not.toHaveBeenCalled()
    })

    it('应该处理过期的token', () => {
      mockReq.headers = {
        authorization: 'Bearer expired-token',
      }

      const expiredError = new Error('Token expired')
      expiredError.name = 'TokenExpiredError'
      vi.mocked(jwt.verify).mockImplementation(() => {
        throw expiredError
      })

      authenticateToken(mockReq as AuthenticatedRequest, mockRes as Response, mockNext)

      expect(statusSpy).toHaveBeenCalledWith(401)
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'TOKEN_EXPIRED',
          message: '访问令牌已过期',
        },
      })
      expect(mockNext).not.toHaveBeenCalled()
    })

    it('应该处理JWT格式错误', () => {
      mockReq.headers = {
        authorization: 'Bearer malformed-token',
      }

      const jwtError = new Error('JWT malformed')
      jwtError.name = 'JsonWebTokenError'
      vi.mocked(jwt.verify).mockImplementation(() => {
        throw jwtError
      })

      authenticateToken(mockReq as AuthenticatedRequest, mockRes as Response, mockNext)

      expect(statusSpy).toHaveBeenCalledWith(401)
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '令牌格式错误',
        },
      })
      expect(mockNext).not.toHaveBeenCalled()
    })

    it('应该处理缺少JWT_SECRET的情况', () => {
      delete process.env.JWT_SECRET

      mockReq.headers = {
        authorization: 'Bearer some-token',
      }

      authenticateToken(mockReq as AuthenticatedRequest, mockRes as Response, mockNext)

      expect(statusSpy).toHaveBeenCalledWith(500)
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'JWT密钥未配置',
        },
      })
      expect(mockNext).not.toHaveBeenCalled()
    })
  })

  describe('token payload验证', () => {
    it('应该验证token包含必要的用户信息', () => {
      const mockPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
        iat: Date.now() / 1000,
        exp: Date.now() / 1000 + 3600,
      }

      mockReq.headers = {
        authorization: 'Bearer valid-token',
      }

      vi.mocked(jwt.verify).mockReturnValue(mockPayload)

      authenticateToken(mockReq as AuthenticatedRequest, mockRes as Response, mockNext)

      expect(mockReq.user).toEqual(mockPayload)
      expect(mockReq.user?.userId).toBe('user-123')
      expect(mockReq.user?.email).toBe('<EMAIL>')
      expect(mockNext).toHaveBeenCalled()
    })

    it('应该拒绝缺少userId的token', () => {
      const mockPayload = {
        email: '<EMAIL>',
        iat: Date.now() / 1000,
        exp: Date.now() / 1000 + 3600,
      }

      mockReq.headers = {
        authorization: 'Bearer incomplete-token',
      }

      vi.mocked(jwt.verify).mockReturnValue(mockPayload)

      authenticateToken(mockReq as AuthenticatedRequest, mockRes as Response, mockNext)

      expect(statusSpy).toHaveBeenCalledWith(401)
      expect(jsonSpy).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '令牌信息不完整',
        },
      })
      expect(mockNext).not.toHaveBeenCalled()
    })
  })
})
