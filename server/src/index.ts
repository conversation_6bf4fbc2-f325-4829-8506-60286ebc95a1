import dotenv from 'dotenv'
import { createApp } from './app.js'

// 加载环境变量
dotenv.config()

async function startServer() {
  try {
    const { app, server, io } = createApp()
    
    const port = parseInt(process.env.PORT || '8989', 10)
    const host = process.env.HOST || 'localhost'

    server.listen(port, host, () => {
      console.log(`🚀 服务器启动成功`)
      console.log(`📍 地址: http://${host}:${port}`)
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`)
      console.log(`📊 健康检查: http://${host}:${port}/health`)
      
      // 显示可用的API端点
      console.log('\n📋 可用的API端点:')
      console.log('  🔐 认证相关:')
      console.log('    POST /api/auth/register   - 用户注册')
      console.log('    POST /api/auth/login      - 用户登录')
      console.log('    POST /api/auth/refresh    - 刷新令牌')
      console.log('    POST /api/auth/logout     - 用户登出')
      
      console.log('  👤 用户相关:')
      console.log('    GET  /api/users/profile   - 获取用户信息')
      console.log('    PUT  /api/users/profile   - 更新用户信息')
      console.log('    DELETE /api/users/profile - 删除用户账户')
      
      console.log('  📚 小说相关:')
      console.log('    GET  /api/novels          - 获取小说列表')
      console.log('    POST /api/novels          - 创建新小说')
      console.log('    GET  /api/novels/:id      - 获取小说详情')
      
      console.log('  🤖 AI相关:')
      console.log('    GET  /api/ai/models       - 获取AI模型状态')
      console.log('    POST /api/ai/generate     - AI内容生成')
      console.log('    POST /api/ai/analyze-style - 分析文本风格')
      console.log('    POST /api/ai/generate-outline - 生成故事大纲')
      console.log('    POST /api/ai/summarize    - 总结文本内容')
      console.log('    POST /api/ai/smart-continue - 智能续写')
      
      console.log('\n🔌 WebSocket事件:')
      console.log('    join_novel     - 加入小说协作')
      console.log('    leave_novel    - 离开小说协作')
      console.log('    content_change - 内容变更')
      console.log('    cursor_change  - 光标位置变更')
    })

    // 优雅关闭处理
    const gracefulShutdown = (signal: string) => {
      console.log(`\n📴 收到 ${signal} 信号，开始优雅关闭...`)
      
      server.close(() => {
        console.log('✅ HTTP服务器已关闭')
        
        // 关闭Socket.IO
        io.close(() => {
          console.log('✅ Socket.IO服务器已关闭')
          
          // 关闭数据库连接等其他资源
          // TODO: 添加Prisma连接关闭
          
          console.log('👋 服务器已完全关闭')
          process.exit(0)
        })
      })

      // 强制关闭超时
      setTimeout(() => {
        console.error('⚠️ 强制关闭服务器')
        process.exit(1)
      }, 10000)
    }

    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
    process.on('SIGINT', () => gracefulShutdown('SIGINT'))

    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      console.error('❌ 未捕获的异常:', error)
      gracefulShutdown('uncaughtException')
    })

    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ 未处理的Promise拒绝:', reason)
      console.error('Promise:', promise)
      gracefulShutdown('unhandledRejection')
    })

  } catch (error) {
    console.error('❌ 服务器启动失败:', error)
    process.exit(1)
  }
}

// 启动服务器
startServer()
