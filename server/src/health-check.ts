/**
 * 健康检查脚本
 * 用于 Docker 容器健康检查
 */

import http from 'http'

const PORT = process.env.PORT || 8989
const TIMEOUT = 3000

function healthCheck(): Promise<boolean> {
  return new Promise((resolve) => {
    const req = http.request(
      {
        hostname: 'localhost',
        port: PORT,
        path: '/health',
        method: 'GET',
        timeout: TIMEOUT,
      },
      (res) => {
        if (res.statusCode === 200) {
          resolve(true)
        } else {
          resolve(false)
        }
      }
    )

    req.on('error', () => {
      resolve(false)
    })

    req.on('timeout', () => {
      req.destroy()
      resolve(false)
    })

    req.end()
  })
}

async function main() {
  try {
    const isHealthy = await healthCheck()
    if (isHealthy) {
      console.log('Health check passed')
      process.exit(0)
    } else {
      console.log('Health check failed')
      process.exit(1)
    }
  } catch (error) {
    console.error('Health check error:', error)
    process.exit(1)
  }
}

main()
