import type {
  AIGenerationRequest,
  AIGenerationResponse,
  AIProvider,
  StyleAnalysis,
  OutlineStructure,
  TextSummary
} from '@shared/index.js'
import { AIServiceError, AI_ERROR_CODES } from '@shared/index.js'
import { GeminiProvider } from './providers/gemini-provider.js'
import { OllamaProvider } from './providers/ollama-provider.js'

export class AIService {
  private providers: AIProvider[]
  private primaryProvider: AIProvider
  private fallbackProvider: AIProvider | null

  constructor() {
    // 初始化提供者
    this.providers = []
    
    try {
      this.primaryProvider = new GeminiProvider()
      this.providers.push(this.primaryProvider)
    } catch (error) {
      console.warn('Gemini提供者初始化失败:', error)
    }

    try {
      this.fallbackProvider = new OllamaProvider()
      this.providers.push(this.fallbackProvider)
    } catch (error) {
      console.warn('Ollama提供者初始化失败:', error)
      this.fallbackProvider = null
    }

    if (this.providers.length === 0) {
      throw new Error('没有可用的AI服务提供者')
    }
  }

  /**
   * 生成文本内容
   */
  async generateText(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    // 首先尝试主要提供者（Gemini）
    if (this.primaryProvider && await this.primaryProvider.isAvailable()) {
      try {
        return await this.primaryProvider.generateText(request)
      } catch (error) {
        console.warn('主要提供者生成失败，尝试备用提供者:', error)
      }
    }

    // 如果主要提供者失败，尝试备用提供者（Ollama）
    if (this.fallbackProvider && await this.fallbackProvider.isAvailable()) {
      try {
        return await this.fallbackProvider.generateText(request)
      } catch (error) {
        console.error('备用提供者也失败:', error)
      }
    }

    throw new Error('没有可用的AI服务提供者')
  }

  /**
   * 分析文本风格
   */
  async analyzeStyle(text: string): Promise<StyleAnalysis> {
    const provider = await this.getAvailableProvider()
    return provider.analyzeStyle(text)
  }

  /**
   * 生成故事大纲
   */
  async generateOutline(synopsis: string, styleFeatures?: StyleAnalysis): Promise<OutlineStructure> {
    const provider = await this.getAvailableProvider()
    return provider.generateOutline(synopsis, styleFeatures)
  }

  /**
   * 总结文本内容
   */
  async summarizeText(text: string): Promise<TextSummary> {
    const provider = await this.getAvailableProvider()
    return provider.summarizeText(text)
  }

  /**
   * 获取可用的提供者
   */
  private async getAvailableProvider(): Promise<AIProvider> {
    // 优先使用主要提供者
    if (this.primaryProvider && await this.primaryProvider.isAvailable()) {
      return this.primaryProvider
    }

    // 备用提供者
    if (this.fallbackProvider && await this.fallbackProvider.isAvailable()) {
      return this.fallbackProvider
    }

    throw new Error('没有可用的AI服务提供者')
  }

  /**
   * 获取所有提供者的状态
   */
  async getProvidersStatus() {
    const status = []
    
    if (this.primaryProvider) {
      status.push({
        provider: 'gemini',
        available: await this.primaryProvider.isAvailable(),
        model: this.primaryProvider.getModelInfo(),
      })
    }

    if (this.fallbackProvider) {
      status.push({
        provider: 'ollama',
        available: await this.fallbackProvider.isAvailable(),
        model: this.fallbackProvider.getModelInfo(),
      })
    }

    return status
  }

  /**
   * 智能续写 - 根据上下文和风格特征生成内容
   */
  async smartContinue(
    currentContent: string,
    context?: {
      previousChapters?: string[]
      characterProfiles?: Record<string, any>
      worldSettings?: Record<string, any>
      styleGuidelines?: StyleAnalysis
    }
  ): Promise<AIGenerationResponse> {
    // 构建智能续写请求
    let prompt = `请续写以下内容：\n\n${currentContent}`
    let contextInfo = ''

    if (context?.previousChapters && context.previousChapters.length > 0) {
      contextInfo += `\n\n前文摘要：\n${context.previousChapters.slice(-2).join('\n\n')}`
    }

    if (context?.characterProfiles) {
      const characters = Object.entries(context.characterProfiles)
        .map(([name, profile]) => `${name}: ${JSON.stringify(profile)}`)
        .join('\n')
      contextInfo += `\n\n角色设定：\n${characters}`
    }

    if (context?.worldSettings) {
      const settings = Object.entries(context.worldSettings)
        .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
        .join('\n')
      contextInfo += `\n\n世界设定：\n${settings}`
    }

    if (context?.styleGuidelines) {
      contextInfo += `\n\n风格要求：\n${JSON.stringify(context.styleGuidelines)}`
    }

    const request: AIGenerationRequest = {
      type: 'continue',
      prompt,
      context: contextInfo,
      parameters: {
        temperature: 0.8, // 稍高的创造性
        maxTokens: 1500,
      },
    }

    return this.generateText(request)
  }

  /**
   * 智能改写 - 改善文本质量
   */
  async smartRewrite(
    content: string,
    improvements: string[] = ['流畅度', '文学性', '描述性']
  ): Promise<AIGenerationResponse> {
    const prompt = `请改写以下内容，重点改善：${improvements.join('、')}

原文：
${content}

要求：
1. 保持原意不变
2. 提升语言表达质量
3. 增强文学性和可读性
4. 保持原有的风格特色`

    const request: AIGenerationRequest = {
      type: 'rewrite',
      prompt,
      parameters: {
        temperature: 0.6, // 中等创造性，保持原意
        maxTokens: Math.max(content.length * 1.5, 1000),
      },
    }

    return this.generateText(request)
  }

  /**
   * 生成角色档案
   */
  async generateCharacter(
    description: string,
    novelContext?: {
      genre?: string
      setting?: string
      existingCharacters?: string[]
    }
  ): Promise<AIGenerationResponse> {
    let prompt = `请根据以下描述创建详细的角色档案：

${description}

请包含以下信息：
1. 基本信息（姓名、年龄、职业等）
2. 外貌描述
3. 性格特征
4. 背景故事
5. 技能和能力
6. 目标和动机
7. 弱点和恐惧`

    if (novelContext) {
      let contextInfo = ''
      if (novelContext.genre) {
        contextInfo += `\n体裁：${novelContext.genre}`
      }
      if (novelContext.setting) {
        contextInfo += `\n背景设定：${novelContext.setting}`
      }
      if (novelContext.existingCharacters) {
        contextInfo += `\n已有角色：${novelContext.existingCharacters.join('、')}`
      }
      
      if (contextInfo) {
        prompt += `\n\n小说背景：${contextInfo}`
      }
    }

    const request: AIGenerationRequest = {
      type: 'character',
      prompt,
      parameters: {
        temperature: 0.8,
        maxTokens: 2000,
      },
    }

    return this.generateText(request)
  }
}
