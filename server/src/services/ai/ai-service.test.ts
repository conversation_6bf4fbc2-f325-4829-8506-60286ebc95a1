import { describe, it, expect, beforeEach, vi } from 'vitest'
import { AIService } from './ai-service.js'
import { GeminiProvider } from './providers/gemini-provider.js'
import { OllamaProvider } from './providers/ollama-provider.js'
import type { AIGenerationRequest, AIGenerationResponse } from '@shared/index.js'

// Mock providers
vi.mock('./providers/gemini-provider.js')
vi.mock('./providers/ollama-provider.js')

describe('AIService', () => {
  let aiService: AIService
  let mockGeminiProvider: any
  let mockOllamaProvider: any

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()
    
    // Create mock providers
    mockGeminiProvider = {
      isAvailable: vi.fn().mockResolvedValue(true),
      generateText: vi.fn(),
      analyzeStyle: vi.fn(),
      generateOutline: vi.fn(),
      summarizeText: vi.fn(),
    }
    
    mockOllamaProvider = {
      isAvailable: vi.fn().mockResolvedValue(true),
      generateText: vi.fn(),
      analyzeStyle: vi.fn(),
      generateOutline: vi.fn(),
      summarizeText: vi.fn(),
    }

    // Mock constructors
    vi.mocked(GeminiProvider).mockImplementation(() => mockGeminiProvider)
    vi.mocked(OllamaProvider).mockImplementation(() => mockOllamaProvider)

    aiService = new AIService()
  })

  describe('初始化', () => {
    it('应该成功创建AI服务实例', () => {
      expect(aiService).toBeInstanceOf(AIService)
    })

    it('应该初始化Gemini和Ollama提供者', () => {
      expect(GeminiProvider).toHaveBeenCalled()
      expect(OllamaProvider).toHaveBeenCalled()
    })
  })

  describe('文本生成', () => {
    it('应该使用Gemini作为主要提供者生成文本', async () => {
      const request: AIGenerationRequest = {
        type: 'continue',
        prompt: '请续写这个故事：从前有一个...',
        context: '这是一个奇幻小说的开头',
      }

      const expectedResponse: AIGenerationResponse = {
        id: 'test-id',
        type: 'continue',
        result: '从前有一个勇敢的骑士...',
        model: {
          provider: 'gemini',
          model: 'gemini-2.0-flash',
          capabilities: ['text_generation'],
        },
        createdAt: new Date(),
      }

      mockGeminiProvider.generateText.mockResolvedValue(expectedResponse)

      const result = await aiService.generateText(request)

      expect(mockGeminiProvider.generateText).toHaveBeenCalledWith(request)
      expect(result).toEqual(expectedResponse)
    })

    it('当Gemini不可用时应该回退到Ollama', async () => {
      const request: AIGenerationRequest = {
        type: 'continue',
        prompt: '请续写这个故事：从前有一个...',
      }

      mockGeminiProvider.isAvailable.mockResolvedValue(false)
      mockOllamaProvider.generateText.mockResolvedValue({
        id: 'ollama-id',
        type: 'continue',
        result: '从前有一个智慧的法师...',
        model: {
          provider: 'ollama',
          model: 'llama3.2',
          capabilities: ['text_generation'],
        },
        createdAt: new Date(),
      })

      const result = await aiService.generateText(request)

      expect(mockGeminiProvider.generateText).not.toHaveBeenCalled()
      expect(mockOllamaProvider.generateText).toHaveBeenCalledWith(request)
      expect(result.model.provider).toBe('ollama')
    })

    it('当所有提供者都不可用时应该抛出错误', async () => {
      const request: AIGenerationRequest = {
        type: 'continue',
        prompt: '请续写这个故事：从前有一个...',
      }

      mockGeminiProvider.isAvailable.mockResolvedValue(false)
      mockOllamaProvider.isAvailable.mockResolvedValue(false)

      await expect(aiService.generateText(request)).rejects.toThrow('没有可用的AI服务提供者')
    })
  })

  describe('风格分析', () => {
    it('应该分析文本风格并返回特征', async () => {
      const sampleText = '这是一段具有特定风格的文本样本...'
      const expectedAnalysis = {
        tone: 'formal',
        style: 'descriptive',
        vocabulary: 'advanced',
        sentence_structure: 'complex',
        themes: ['adventure', 'mystery'],
      }

      mockGeminiProvider.analyzeStyle.mockResolvedValue(expectedAnalysis)

      const result = await aiService.analyzeStyle(sampleText)

      expect(mockGeminiProvider.analyzeStyle).toHaveBeenCalledWith(sampleText)
      expect(result).toEqual(expectedAnalysis)
    })
  })

  describe('大纲生成', () => {
    it('应该根据概要生成故事大纲', async () => {
      const synopsis = '一个关于时间旅行的科幻故事'
      const styleFeatures = { tone: 'serious', style: 'narrative' }
      
      const expectedOutline = {
        structure: [
          {
            id: 'act1',
            title: '第一幕：发现',
            type: 'act' as const,
            orderIndex: 0,
            status: 'planned' as const,
            children: [
              {
                id: 'ch1',
                title: '第一章：意外的发现',
                type: 'chapter' as const,
                orderIndex: 0,
                status: 'planned' as const,
              }
            ]
          }
        ]
      }

      mockGeminiProvider.generateOutline.mockResolvedValue(expectedOutline)

      const result = await aiService.generateOutline(synopsis, styleFeatures)

      expect(mockGeminiProvider.generateOutline).toHaveBeenCalledWith(synopsis, styleFeatures)
      expect(result.structure).toHaveLength(1)
      expect(result.structure[0].title).toBe('第一幕：发现')
    })
  })

  describe('文本总结', () => {
    it('应该总结长文本并提取关键信息', async () => {
      const longText = '这是一段很长的文本内容...'
      const expectedSummary = {
        summary: '文本的简要总结',
        keyPoints: ['关键点1', '关键点2'],
        unresolved: ['未解决的线索1'],
        characters: ['角色A', '角色B'],
      }

      mockGeminiProvider.summarizeText.mockResolvedValue(expectedSummary)

      const result = await aiService.summarizeText(longText)

      expect(mockGeminiProvider.summarizeText).toHaveBeenCalledWith(longText)
      expect(result).toEqual(expectedSummary)
    })
  })

  describe('错误处理', () => {
    it('应该处理提供者抛出的错误', async () => {
      const request: AIGenerationRequest = {
        type: 'continue',
        prompt: '测试提示',
      }

      mockGeminiProvider.generateText.mockRejectedValue(new Error('API调用失败'))
      mockOllamaProvider.generateText.mockResolvedValue({
        id: 'fallback-id',
        type: 'continue',
        result: '备用结果',
        model: { provider: 'ollama', model: 'llama3.2', capabilities: ['text_generation'] },
        createdAt: new Date(),
      })

      const result = await aiService.generateText(request)

      expect(result.model.provider).toBe('ollama')
    })
  })
})
