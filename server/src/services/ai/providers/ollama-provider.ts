import { O<PERSON>ma } from 'ollama'
import type {
  AIGenerationRequest,
  AIGenerationResponse,
  AIModel,
  AIProvider,
  StyleAnalysis,
  OutlineStructure,
  TextSummary,
  GenerationParameters
} from '@shared/index.js'
import { AIServiceError, AI_ERROR_CODES } from '@shared/index.js'
import { v4 as uuidv4 } from 'uuid'

export class OllamaProvider implements AIProvider {
  public readonly name = 'ollama'
  private ollama: Ollama
  private model: string
  private enabled: boolean

  constructor() {
    this.enabled = process.env.OLLAMA_ENABLED === 'true'
    this.model = process.env.OLLAMA_MODEL || 'llama3.2'
    
    if (this.enabled) {
      this.ollama = new Ollama({
        host: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
      })
    }
  }

  async isAvailable(): Promise<boolean> {
    if (!this.enabled) return false
    
    try {
      await this.ollama.list()
      return true
    } catch {
      return false
    }
  }

  async generateText(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    if (!this.enabled) {
      throw new AIServiceError(
        'Ollama服务未启用',
        AI_ERROR_CODES.PROVIDER_UNAVAILABLE,
        'ollama'
      )
    }

    try {
      const prompt = this.buildPrompt(request)
      
      const response = await this.ollama.generate({
        model: this.model,
        prompt,
        options: {
          temperature: request.parameters?.temperature ?? 0.7,
          num_predict: request.parameters?.maxTokens ?? 2000,
          top_p: request.parameters?.topP ?? 0.9,
        },
      })

      return {
        id: uuidv4(),
        type: request.type,
        result: response.response,
        model: {
          provider: 'ollama',
          model: this.model,
          capabilities: ['text_generation'],
        },
        usage: {
          promptTokens: 0, // Ollama不提供详细的token统计
          completionTokens: 0,
          totalTokens: 0,
        },
        createdAt: new Date(),
      }
    } catch (error) {
      throw new AIServiceError(
        'Ollama API调用失败',
        AI_ERROR_CODES.GENERATION_FAILED,
        'ollama',
        error as Error
      )
    }
  }

  async analyzeStyle(text: string): Promise<StyleAnalysis> {
    const prompt = `请分析以下文本的写作风格：

${text}

请以JSON格式返回分析结果，包含tone, style, vocabulary, sentence_structure, themes, pacing, perspective, genre_indicators字段。`

    try {
      const response = await this.ollama.generate({
        model: this.model,
        prompt,
        options: { temperature: 0.1 },
      })

      // 尝试解析JSON，如果失败则返回默认值
      try {
        return JSON.parse(response.response)
      } catch {
        return {
          tone: 'neutral',
          style: 'narrative',
          complexity: 0.5,
          vocabulary: ['intermediate'],
          pacing: 'moderate',
          themes: ['general'],
          patterns: {},
          suggestions: ['基于Ollama的风格分析建议']
        }
      }
    } catch (error) {
      throw new AIServiceError(
        'Ollama风格分析失败',
        AI_ERROR_CODES.GENERATION_FAILED,
        'ollama',
        error as Error
      )
    }
  }

  async generateOutline(synopsis: string, styleFeatures?: StyleAnalysis): Promise<OutlineStructure> {
    let prompt = `请根据以下概要生成故事大纲：

${synopsis}

请以JSON格式返回大纲结构。`

    if (styleFeatures) {
      prompt += `\n\n风格要求：${JSON.stringify(styleFeatures)}`
    }

    try {
      const response = await this.ollama.generate({
        model: this.model,
        prompt,
        options: { temperature: 0.7 },
      })

      try {
        return JSON.parse(response.response)
      } catch {
        // 返回默认大纲结构
        return {
          structure: [
            {
              id: 'act1',
              title: '第一幕',
              type: 'act',
              orderIndex: 0,
              status: 'planned',
              children: [
                {
                  id: 'ch1',
                  title: '第一章',
                  type: 'chapter',
                  orderIndex: 0,
                  status: 'planned',
                }
              ]
            }
          ]
        }
      }
    } catch (error) {
      throw new AIServiceError(
        'Ollama大纲生成失败',
        AI_ERROR_CODES.GENERATION_FAILED,
        'ollama',
        error as Error
      )
    }
  }

  async summarizeText(text: string): Promise<TextSummary> {
    const prompt = `请总结以下文本内容：

${text}

请以JSON格式返回总结，包含summary, keyPoints, unresolved, characters, locations, timeline, mood, conflicts字段。`

    try {
      const response = await this.ollama.generate({
        model: this.model,
        prompt,
        options: { temperature: 0.3 },
      })

      try {
        return JSON.parse(response.response)
      } catch {
        return {
          summary: '文本总结',
          keyPoints: [],
          unresolved: [],
          characters: [],
          locations: [],
          timeline: [],
          mood: 'neutral',
          conflicts: [],
        }
      }
    } catch (error) {
      throw new AIServiceError(
        'Ollama文本总结失败',
        AI_ERROR_CODES.GENERATION_FAILED,
        'ollama',
        error as Error
      )
    }
  }



  private buildPrompt(request: AIGenerationRequest): string {
    const { type, prompt, context } = request
    
    let systemPrompt = ''
    
    switch (type) {
      case 'continue':
        systemPrompt = '你是一个专业的小说续写助手。请根据给定的内容继续写作，保持风格一致。'
        break
      case 'rewrite':
        systemPrompt = '你是一个专业的文本改写助手。请改写给定的内容，改善表达方式。'
        break
      case 'character':
        systemPrompt = '你是一个专业的角色设计助手。请创建详细的角色档案。'
        break
      default:
        systemPrompt = '你是一个专业的写作助手。'
    }

    let fullPrompt = systemPrompt + '\n\n'
    
    if (context) {
      fullPrompt += `上下文：${context}\n\n`
    }
    
    fullPrompt += `要求：${prompt}`
    
    return fullPrompt
  }

  async smartContinue(
    content: string,
    context?: {
      previousChapters?: string[]
      characterProfiles?: Record<string, any>
      worldSettings?: Record<string, any>
      styleGuidelines?: StyleAnalysis
    }
  ): Promise<AIGenerationResponse> {
    const request: AIGenerationRequest = {
      type: 'continue',
      prompt: content,
      context: JSON.stringify(context),
      parameters: { temperature: 0.7, maxTokens: 1000 }
    }
    return this.generateText(request)
  }

  getModelInfo(): AIModel {
    return {
      provider: 'ollama',
      model: this.model,
      version: '1.0',
      capabilities: ['text_generation', 'analysis']
    }
  }
}
