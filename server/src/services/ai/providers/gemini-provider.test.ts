import { describe, it, expect, beforeEach, vi } from 'vitest'
import { GeminiProvider } from './gemini-provider.js'
import type { AIGenerationRequest } from '@shared/index.js'

// Mock Google Generative AI
const mockGenerateContent = vi.fn()
const mockGetGenerativeModel = vi.fn(() => ({
  generateContent: mockGenerateContent,
}))

vi.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: vi.fn(() => ({
    getGenerativeModel: mockGetGenerativeModel,
  })),
}))

describe('GeminiProvider', () => {
  let provider: GeminiProvider

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock environment variables
    process.env.GEMINI_API_KEY = 'test-api-key'
    process.env.GEMINI_MODEL_FAST = 'gemini-2.0-flash'
    process.env.GEMINI_MODEL_PRO = 'gemini-2.5-pro'
    
    provider = new GeminiProvider()
  })

  describe('初始化', () => {
    it('应该成功创建Gemini提供者实例', () => {
      expect(provider).toBeInstanceOf(GeminiProvider)
    })

    it('当没有API密钥时应该抛出错误', () => {
      delete process.env.GEMINI_API_KEY
      expect(() => new GeminiProvider()).toThrow('Gemini API密钥未配置')
    })
  })

  describe('可用性检查', () => {
    it('应该返回true当API密钥存在时', async () => {
      const isAvailable = await provider.isAvailable()
      expect(isAvailable).toBe(true)
    })

    it('应该返回false当API密钥不存在时', async () => {
      delete process.env.GEMINI_API_KEY
      provider = new GeminiProvider()
      const isAvailable = await provider.isAvailable()
      expect(isAvailable).toBe(false)
    })
  })

  describe('文本生成', () => {
    it('应该生成续写内容', async () => {
      const request: AIGenerationRequest = {
        type: 'continue',
        prompt: '请续写这个故事：从前有一个勇敢的骑士',
        context: '这是一个中世纪奇幻故事',
      }

      const mockResponse = {
        response: {
          text: () => '从前有一个勇敢的骑士，他踏上了寻找圣杯的旅程...',
          usageMetadata: {
            promptTokenCount: 20,
            candidatesTokenCount: 50,
            totalTokenCount: 70,
          },
        },
      }

      mockGenerateContent.mockResolvedValue(mockResponse)

      const result = await provider.generateText(request)

      expect(result.type).toBe('continue')
      expect(result.result).toContain('从前有一个勇敢的骑士')
      expect(result.model.provider).toBe('gemini')
      expect(result.usage?.totalTokens).toBe(70)
    })

    it('应该使用正确的模型进行不同类型的生成', async () => {
      const outlineRequest: AIGenerationRequest = {
        type: 'outline',
        prompt: '生成一个科幻小说大纲',
      }

      mockGenerateContent.mockResolvedValue({
        response: {
          text: () => '科幻小说大纲...',
          usageMetadata: { totalTokenCount: 100 },
        },
      })

      await provider.generateText(outlineRequest)

      // 大纲生成应该使用Pro模型
      expect(mockGetGenerativeModel).toHaveBeenCalledWith({
        model: 'gemini-2.5-pro',
        generationConfig: expect.any(Object),
      })
    })

    it('应该处理API错误', async () => {
      const request: AIGenerationRequest = {
        type: 'continue',
        prompt: '测试提示',
      }

      mockGenerateContent.mockRejectedValue(new Error('API调用失败'))

      await expect(provider.generateText(request)).rejects.toThrow('Gemini API调用失败')
    })
  })

  describe('风格分析', () => {
    it('应该分析文本风格', async () => {
      const sampleText = '这是一段优美的散文，描述了春天的景色...'
      
      const mockResponse = {
        response: {
          text: () => JSON.stringify({
            tone: 'poetic',
            style: 'descriptive',
            vocabulary: 'advanced',
            sentence_structure: 'complex',
            themes: ['nature', 'beauty'],
            pacing: 'slow',
            perspective: 'third-person',
            genre_indicators: ['literary', 'descriptive'],
          }),
        },
      }

      mockGenerateContent.mockResolvedValue(mockResponse)

      const result = await provider.analyzeStyle(sampleText)

      expect(result.tone).toBe('poetic')
      expect(result.style).toBe('descriptive')
      expect(result.themes).toContain('nature')
    })

    it('应该处理无效的JSON响应', async () => {
      const sampleText = '测试文本'
      
      mockGenerateContent.mockResolvedValue({
        response: {
          text: () => '无效的JSON响应',
        },
      })

      await expect(provider.analyzeStyle(sampleText)).rejects.toThrow('风格分析结果解析失败')
    })
  })

  describe('大纲生成', () => {
    it('应该生成结构化的故事大纲', async () => {
      const synopsis = '一个关于时间旅行的科幻故事'
      const styleFeatures = {
        tone: 'serious',
        style: 'narrative',
        vocabulary: 'intermediate',
        sentence_structure: 'varied',
        themes: ['science-fiction', 'time-travel'],
        pacing: 'fast',
        perspective: 'third-person',
        genre_indicators: ['sci-fi'],
      }

      const mockOutline = {
        structure: [
          {
            id: 'act1',
            title: '第一幕：发现',
            type: 'act',
            orderIndex: 0,
            status: 'planned',
            children: [
              {
                id: 'ch1',
                title: '第一章：时间机器',
                type: 'chapter',
                orderIndex: 0,
                status: 'planned',
                estimatedWordCount: 3000,
              }
            ]
          }
        ]
      }

      mockGenerateContent.mockResolvedValue({
        response: {
          text: () => JSON.stringify(mockOutline),
        },
      })

      const result = await provider.generateOutline(synopsis, styleFeatures)

      expect(result.structure).toHaveLength(1)
      expect(result.structure[0].title).toBe('第一幕：发现')
      expect(result.structure[0].children).toHaveLength(1)
    })
  })

  describe('文本总结', () => {
    it('应该总结长文本内容', async () => {
      const longText = '这是一段很长的故事内容，包含多个角色和情节...'
      
      const mockSummary = {
        summary: '故事讲述了主角的冒险历程',
        keyPoints: ['主角出发', '遇到困难', '解决问题'],
        unresolved: ['反派的真实身份'],
        characters: ['主角', '导师', '反派'],
        locations: ['村庄', '森林', '城堡'],
        timeline: ['第一天', '第二天', '第三天'],
        mood: 'adventurous',
        conflicts: ['内心冲突', '外部威胁'],
      }

      mockGenerateContent.mockResolvedValue({
        response: {
          text: () => JSON.stringify(mockSummary),
        },
      })

      const result = await provider.summarizeText(longText)

      expect(result.summary).toBe('故事讲述了主角的冒险历程')
      expect(result.keyPoints).toHaveLength(3)
      expect(result.characters).toContain('主角')
    })
  })

  describe('模型信息', () => {
    it('应该返回正确的模型信息', () => {
      const modelInfo = provider.getModelInfo()
      
      expect(modelInfo.provider).toBe('gemini')
      expect(modelInfo.capabilities).toContain('text_generation')
      expect(modelInfo.capabilities).toContain('analysis')
    })
  })
})
