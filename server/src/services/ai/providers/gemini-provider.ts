import { GoogleGenerativeAI } from '@google/generative-ai'
import type {
  AIGenerationRequest,
  AIGenerationResponse,
  AIModel,
  AIProvider,
  StyleAnalysis,
  OutlineStructure,
  TextSummary,
  GenerationParameters
} from '@shared/index.js'
import { AIServiceError, AI_ERROR_CODES } from '@shared/index.js'
import { v4 as uuidv4 } from 'uuid'

export class GeminiProvider implements AIProvider {
  public readonly name = 'gemini'
  private genAI: GoogleGenerativeAI
  private apiKey: string
  private models: {
    fast: string
    pro: string
    lite: string
  }

  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY || ''
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置')
    }

    this.genAI = new GoogleGenerativeAI(this.apiKey)
    this.models = {
      fast: process.env.GEMINI_MODEL_FAST || 'gemini-2.0-flash',
      pro: process.env.GEMINI_MODEL_PRO || 'gemini-2.5-pro',
      lite: process.env.GEMINI_MODEL_LITE || 'gemini-2.0-flash-lite',
    }
  }

  async isAvailable(): Promise<boolean> {
    try {
      return !!this.apiKey
    } catch {
      return false
    }
  }

  async generateText(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    try {
      const model = this.selectModel(request.type)
      const prompt = this.buildPrompt(request)
      const parameters = this.buildGenerationConfig(request.parameters)

      const generativeModel = this.genAI.getGenerativeModel({
        model,
        generationConfig: parameters,
      })

      const result = await generativeModel.generateContent(prompt)
      const response = result.response
      const text = response.text()

      return {
        id: uuidv4(),
        type: request.type,
        result: text,
        model: {
          provider: 'gemini',
          model,
          capabilities: ['text_generation', 'analysis'],
        },
        usage: response.usageMetadata ? {
          promptTokens: response.usageMetadata.promptTokenCount || 0,
          completionTokens: response.usageMetadata.candidatesTokenCount || 0,
          totalTokens: response.usageMetadata.totalTokenCount || 0,
        } : undefined,
        createdAt: new Date(),
      }
    } catch (error) {
      throw new AIServiceError(
        'Gemini API调用失败',
        AI_ERROR_CODES.GENERATION_FAILED,
        'gemini',
        error as Error
      )
    }
  }

  async analyzeStyle(text: string): Promise<StyleAnalysis> {
    try {
      const prompt = this.buildStyleAnalysisPrompt(text)
      const model = this.genAI.getGenerativeModel({
        model: this.models.pro, // 使用Pro模型进行深度分析
        generationConfig: {
          temperature: 0.1, // 低温度确保一致性
          maxOutputTokens: 1000,
        },
      })

      const result = await model.generateContent(prompt)
      const responseText = result.response.text()
      
      try {
        return JSON.parse(responseText) as StyleAnalysis
      } catch {
        throw new Error('风格分析结果解析失败')
      }
    } catch (error) {
      throw new AIServiceError(
        '风格分析失败',
        AI_ERROR_CODES.GENERATION_FAILED,
        'gemini',
        error as Error
      )
    }
  }

  async generateOutline(synopsis: string, styleFeatures?: StyleAnalysis): Promise<OutlineStructure> {
    try {
      const prompt = this.buildOutlinePrompt(synopsis, styleFeatures)
      const model = this.genAI.getGenerativeModel({
        model: this.models.pro, // 使用Pro模型生成复杂结构
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 2000,
        },
      })

      const result = await model.generateContent(prompt)
      const responseText = result.response.text()
      
      try {
        return JSON.parse(responseText) as OutlineStructure
      } catch {
        throw new Error('大纲生成结果解析失败')
      }
    } catch (error) {
      throw new AIServiceError(
        '大纲生成失败',
        AI_ERROR_CODES.GENERATION_FAILED,
        'gemini',
        error as Error
      )
    }
  }

  async summarizeText(text: string): Promise<TextSummary> {
    try {
      const prompt = this.buildSummaryPrompt(text)
      const model = this.genAI.getGenerativeModel({
        model: this.models.fast, // 使用Fast模型快速总结
        generationConfig: {
          temperature: 0.3,
          maxOutputTokens: 1500,
        },
      })

      const result = await model.generateContent(prompt)
      const responseText = result.response.text()
      
      try {
        return JSON.parse(responseText) as TextSummary
      } catch {
        throw new Error('文本总结结果解析失败')
      }
    } catch (error) {
      throw new AIServiceError(
        '文本总结失败',
        AI_ERROR_CODES.GENERATION_FAILED,
        'gemini',
        error as Error
      )
    }
  }



  private selectModel(type: string): string {
    switch (type) {
      case 'outline':
      case 'character':
        return this.models.pro // 复杂生成使用Pro模型
      case 'summary':
        return this.models.fast // 快速任务使用Fast模型
      default:
        return this.models.fast // 默认使用Fast模型
    }
  }

  private buildPrompt(request: AIGenerationRequest): string {
    const { type, prompt, context } = request
    
    let systemPrompt = ''
    
    switch (type) {
      case 'continue':
        systemPrompt = '你是一个专业的小说续写助手。请根据给定的内容和上下文，以相同的风格和语调继续写作。保持角色一致性和情节连贯性。'
        break
      case 'rewrite':
        systemPrompt = '你是一个专业的文本改写助手。请改写给定的内容，保持原意但改善表达方式、语言流畅度和文学性。'
        break
      case 'character':
        systemPrompt = '你是一个专业的角色设计助手。请根据描述创建详细的角色档案，包括外貌、性格、背景等。'
        break
      default:
        systemPrompt = '你是一个专业的写作助手。请根据用户的要求提供高质量的内容。'
    }

    let fullPrompt = systemPrompt + '\n\n'
    
    if (context) {
      fullPrompt += `上下文信息：\n${context}\n\n`
    }
    
    fullPrompt += `用户要求：\n${prompt}`
    
    return fullPrompt
  }

  private buildStyleAnalysisPrompt(text: string): string {
    return `请分析以下文本的写作风格，并以JSON格式返回分析结果：

文本内容：
${text}

请返回以下格式的JSON：
{
  "tone": "语调(如formal, casual, humorous, serious等)",
  "style": "风格(如descriptive, narrative, dialogue-heavy等)",
  "vocabulary": "词汇水平(simple, intermediate, advanced)",
  "sentence_structure": "句式结构(simple, complex, varied)",
  "themes": ["主题标签数组"],
  "pacing": "节奏(fast, moderate, slow)",
  "perspective": "视角(first-person, third-person, omniscient)",
  "genre_indicators": ["体裁指标数组"]
}

只返回JSON，不要其他解释。`
  }

  private buildOutlinePrompt(synopsis: string, styleFeatures?: StyleAnalysis): string {
    let prompt = `请根据以下小说概要生成详细的故事大纲：

概要：
${synopsis}

`

    if (styleFeatures) {
      prompt += `风格要求：
- 语调：${styleFeatures.tone}
- 风格：${styleFeatures.style}
- 节奏：${styleFeatures.pacing}
- 主题：${styleFeatures.themes.join(', ')}

`
    }

    prompt += `请返回以下格式的JSON：
{
  "structure": [
    {
      "id": "唯一标识符",
      "title": "标题",
      "description": "描述(可选)",
      "type": "类型(act/chapter/scene/plot_point)",
      "orderIndex": 序号,
      "status": "planned",
      "estimatedWordCount": 预估字数,
      "children": [子节点数组(可选)]
    }
  ]
}

请生成完整的三幕结构，每幕包含多个章节。只返回JSON，不要其他解释。`

    return prompt
  }

  private buildSummaryPrompt(text: string): string {
    return `请总结以下文本内容，并以JSON格式返回分析结果：

文本内容：
${text}

请返回以下格式的JSON：
{
  "summary": "主要内容总结",
  "keyPoints": ["关键情节点数组"],
  "unresolved": ["未解决的线索数组"],
  "characters": ["出现的角色数组"],
  "locations": ["出现的地点数组"],
  "timeline": ["时间线要点数组"],
  "mood": "整体氛围",
  "conflicts": ["冲突点数组"]
}

只返回JSON，不要其他解释。`
  }

  private buildGenerationConfig(parameters?: GenerationParameters) {
    return {
      temperature: parameters?.temperature ?? 0.7,
      maxOutputTokens: parameters?.maxTokens ?? 2000,
      topP: parameters?.topP ?? 0.9,
      topK: 40,
    }
  }

  async smartContinue(
    content: string,
    context?: {
      previousChapters?: string[]
      characterProfiles?: Record<string, any>
      worldSettings?: Record<string, any>
      styleGuidelines?: StyleAnalysis
    }
  ): Promise<AIGenerationResponse> {
    const request: AIGenerationRequest = {
      type: 'continue',
      prompt: content,
      context: JSON.stringify(context),
      parameters: { temperature: 0.7, maxTokens: 1000 }
    }
    return this.generateText(request)
  }

  getModelInfo(): AIModel {
    return {
      provider: 'gemini',
      model: this.models.fast,
      version: '1.0',
      capabilities: ['text_generation', 'analysis']
    }
  }
}
