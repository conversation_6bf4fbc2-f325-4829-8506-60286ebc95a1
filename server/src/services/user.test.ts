import { describe, it, expect, vi, beforeEach } from 'vitest'
import bcrypt from 'bcryptjs'
import { UserService } from './user.js'
import { AppError } from '../middleware/error-handler.js'

// Mock Prisma client
const mockPrismaUser = {
  create: vi.fn(),
  findUnique: vi.fn(),
  findMany: vi.fn(),
  update: vi.fn(),
  delete: vi.fn(),
}

const mockPrisma = {
  user: mockPrismaUser,
}

// Mock bcrypt
vi.mock('bcryptjs')

describe('UserService', () => {
  let userService: UserService

  beforeEach(() => {
    vi.clearAllMocks()
    userService = new UserService(mockPrisma as any)
  })

  describe('createUser', () => {
    it('应该成功创建新用户', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
      }

      const hashedPassword = 'hashed_password_123'
      const createdUser = {
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        password: hashedPassword,
        avatar: null,
        settings: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      vi.mocked(bcrypt.hash).mockResolvedValue(hashedPassword)
      mockPrismaUser.findUnique.mockResolvedValue(null) // 用户不存在
      mockPrismaUser.create.mockResolvedValue(createdUser)

      const result = await userService.createUser(userData)

      expect(bcrypt.hash).toHaveBeenCalledWith('password123', 12)
      expect(mockPrismaUser.create).toHaveBeenCalledWith({
        data: {
          username: 'testuser',
          email: '<EMAIL>',
          password: hashedPassword,
        },
      })
      expect(result).toEqual({
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        avatar: null,
        settings: null,
        createdAt: createdUser.createdAt,
        updatedAt: createdUser.updatedAt,
      })
    })

    it('应该在邮箱已存在时抛出错误', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
      }

      mockPrismaUser.findUnique.mockResolvedValue({
        id: 'existing-user',
        email: '<EMAIL>',
      })

      await expect(userService.createUser(userData)).rejects.toThrow(AppError)
      await expect(userService.createUser(userData)).rejects.toThrow('邮箱已被使用')
    })

    it('应该验证密码强度', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: '123', // 太短的密码
      }

      await expect(userService.createUser(userData)).rejects.toThrow(AppError)
      await expect(userService.createUser(userData)).rejects.toThrow('密码长度至少8位')
    })

    it('应该验证用户名格式', async () => {
      const userData = {
        username: 'ab', // 太短的用户名
        email: '<EMAIL>',
        password: 'password123',
      }

      await expect(userService.createUser(userData)).rejects.toThrow(AppError)
      await expect(userService.createUser(userData)).rejects.toThrow('用户名长度必须在3-50字符之间')
    })

    it('应该验证邮箱格式', async () => {
      const userData = {
        username: 'testuser',
        email: 'invalid-email', // 无效邮箱格式
        password: 'password123',
      }

      await expect(userService.createUser(userData)).rejects.toThrow(AppError)
      await expect(userService.createUser(userData)).rejects.toThrow('邮箱格式不正确')
    })
  })

  describe('authenticateUser', () => {
    it('应该成功验证用户凭据', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'password123',
      }

      const storedUser = {
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        password: 'hashed_password_123',
        avatar: null,
        settings: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockPrismaUser.findUnique.mockResolvedValue(storedUser)
      vi.mocked(bcrypt.compare).mockResolvedValue(true)

      const result = await userService.authenticateUser(credentials)

      expect(mockPrismaUser.findUnique).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      })
      expect(bcrypt.compare).toHaveBeenCalledWith('password123', 'hashed_password_123')
      expect(result).toEqual({
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        avatar: null,
        settings: null,
        createdAt: storedUser.createdAt,
        updatedAt: storedUser.updatedAt,
      })
    })

    it('应该在用户不存在时抛出错误', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'password123',
      }

      mockPrismaUser.findUnique.mockResolvedValue(null)

      await expect(userService.authenticateUser(credentials)).rejects.toThrow(AppError)
      await expect(userService.authenticateUser(credentials)).rejects.toThrow('邮箱或密码错误')
    })

    it('应该在密码错误时抛出错误', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      }

      const storedUser = {
        id: 'user-123',
        email: '<EMAIL>',
        password: 'hashed_password_123',
      }

      mockPrismaUser.findUnique.mockResolvedValue(storedUser)
      vi.mocked(bcrypt.compare).mockResolvedValue(false)

      await expect(userService.authenticateUser(credentials)).rejects.toThrow(AppError)
      await expect(userService.authenticateUser(credentials)).rejects.toThrow('邮箱或密码错误')
    })
  })

  describe('getUserById', () => {
    it('应该成功获取用户信息', async () => {
      const userId = 'user-123'
      const user = {
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        password: 'hashed_password',
        avatar: null,
        settings: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockPrismaUser.findUnique.mockResolvedValue(user)

      const result = await userService.getUserById(userId)

      expect(mockPrismaUser.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
      })
      expect(result).toEqual({
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        avatar: null,
        settings: null,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      })
    })

    it('应该在用户不存在时抛出错误', async () => {
      const userId = 'nonexistent-user'

      mockPrismaUser.findUnique.mockResolvedValue(null)

      await expect(userService.getUserById(userId)).rejects.toThrow(AppError)
      await expect(userService.getUserById(userId)).rejects.toThrow('用户不存在')
    })
  })

  describe('updateUser', () => {
    it('应该成功更新用户信息', async () => {
      const userId = 'user-123'
      const updateData = {
        username: 'newusername',
        avatar: 'https://example.com/avatar.jpg',
      }

      const updatedUser = {
        id: 'user-123',
        username: 'newusername',
        email: '<EMAIL>',
        password: 'hashed_password',
        avatar: 'https://example.com/avatar.jpg',
        settings: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockPrismaUser.update.mockResolvedValue(updatedUser)

      const result = await userService.updateUser(userId, updateData)

      expect(mockPrismaUser.update).toHaveBeenCalledWith({
        where: { id: userId },
        data: updateData,
      })
      expect(result).toEqual({
        id: 'user-123',
        username: 'newusername',
        email: '<EMAIL>',
        avatar: 'https://example.com/avatar.jpg',
        settings: null,
        createdAt: updatedUser.createdAt,
        updatedAt: updatedUser.updatedAt,
      })
    })

    it('应该在更新密码时进行哈希处理', async () => {
      const userId = 'user-123'
      const updateData = {
        password: 'newpassword123',
      }

      const hashedPassword = 'new_hashed_password'
      const updatedUser = {
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        password: hashedPassword,
        avatar: null,
        settings: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      vi.mocked(bcrypt.hash).mockResolvedValue(hashedPassword)
      mockPrismaUser.update.mockResolvedValue(updatedUser)

      await userService.updateUser(userId, updateData)

      expect(bcrypt.hash).toHaveBeenCalledWith('newpassword123', 12)
      expect(mockPrismaUser.update).toHaveBeenCalledWith({
        where: { id: userId },
        data: { password: hashedPassword },
      })
    })
  })
})
