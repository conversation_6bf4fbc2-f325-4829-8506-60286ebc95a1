import bcrypt from 'bcryptjs'
import { PrismaClient } from '@prisma/client'
import { AppError, createError } from '../middleware/error-handler.js'
import type { User } from '@shared/index.js'

export interface CreateUserData {
  username: string
  email: string
  password: string
}

export interface AuthenticateUserData {
  email: string
  password: string
}

export interface UpdateUserData {
  username?: string
  email?: string
  password?: string
  avatar?: string
  settings?: Record<string, any>
}

export class UserService {
  constructor(private prisma: PrismaClient) {}

  /**
   * 创建新用户
   */
  async createUser(userData: CreateUserData): Promise<Omit<User, 'password'>> {
    const { username, email, password } = userData

    // 验证输入数据
    this.validateUserInput({ username, email, password })

    // 检查邮箱是否已存在
    const existingUser = await this.prisma.user.findUnique({
      where: { email },
    })

    if (existingUser) {
      throw createError.conflict('邮箱已被使用')
    }

    // 哈希密码
    const hashedPassword = await bcrypt.hash(password, 12)

    // 创建用户
    const user = await this.prisma.user.create({
      data: {
        username,
        email,
        password: hashedPassword,
      },
    })

    // 返回用户信息（不包含密码）
    return this.excludePassword(user)
  }

  /**
   * 验证用户凭据
   */
  async authenticateUser(credentials: AuthenticateUserData): Promise<Omit<User, 'password'>> {
    const { email, password } = credentials

    // 查找用户
    const user = await this.prisma.user.findUnique({
      where: { email },
    })

    if (!user) {
      throw createError.unauthorized('邮箱或密码错误')
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password)
    if (!isPasswordValid) {
      throw createError.unauthorized('邮箱或密码错误')
    }

    return this.excludePassword(user)
  }

  /**
   * 根据ID获取用户
   */
  async getUserById(userId: string): Promise<Omit<User, 'password'>> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    })

    if (!user) {
      throw createError.notFound('用户不存在')
    }

    return this.excludePassword(user)
  }

  /**
   * 根据邮箱获取用户
   */
  async getUserByEmail(email: string): Promise<Omit<User, 'password'> | null> {
    const user = await this.prisma.user.findUnique({
      where: { email },
    })

    return user ? this.excludePassword(user) : null
  }

  /**
   * 更新用户信息
   */
  async updateUser(userId: string, updateData: UpdateUserData): Promise<Omit<User, 'password'>> {
    const { password, ...otherData } = updateData

    // 如果要更新密码，先进行哈希处理
    let dataToUpdate: any = { ...otherData }
    if (password) {
      this.validatePassword(password)
      dataToUpdate.password = await bcrypt.hash(password, 12)
    }

    // 如果要更新邮箱，检查是否已被使用
    if (updateData.email) {
      const existingUser = await this.prisma.user.findUnique({
        where: { email: updateData.email },
      })
      if (existingUser && existingUser.id !== userId) {
        throw createError.conflict('邮箱已被使用')
      }
    }

    // 如果要更新用户名，进行验证
    if (updateData.username) {
      this.validateUsername(updateData.username)
    }

    try {
      const user = await this.prisma.user.update({
        where: { id: userId },
        data: dataToUpdate,
      })

      return this.excludePassword(user)
    } catch (error: any) {
      if (error.code === 'P2025') {
        throw createError.notFound('用户不存在')
      }
      throw error
    }
  }

  /**
   * 删除用户
   */
  async deleteUser(userId: string): Promise<void> {
    try {
      await this.prisma.user.delete({
        where: { id: userId },
      })
    } catch (error: any) {
      if (error.code === 'P2025') {
        throw createError.notFound('用户不存在')
      }
      throw error
    }
  }

  /**
   * 获取用户列表（管理员功能）
   */
  async getUsers(options: {
    page?: number
    limit?: number
    search?: string
  } = {}): Promise<{
    users: Omit<User, 'password'>[]
    total: number
    page: number
    totalPages: number
  }> {
    const { page = 1, limit = 20, search } = options
    const skip = (page - 1) * limit

    const where = search
      ? {
          OR: [
            { username: { contains: search, mode: 'insensitive' as const } },
            { email: { contains: search, mode: 'insensitive' as const } },
          ],
        }
      : {}

    const [users, total] = await Promise.all([
      this.prisma.user.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.user.count({ where }),
    ])

    return {
      users: users.map(user => this.excludePassword(user)),
      total,
      page,
      totalPages: Math.ceil(total / limit),
    }
  }

  /**
   * 验证用户输入数据
   */
  private validateUserInput(data: { username: string; email: string; password: string }): void {
    this.validateUsername(data.username)
    this.validateEmail(data.email)
    this.validatePassword(data.password)
  }

  /**
   * 验证用户名
   */
  private validateUsername(username: string): void {
    if (!username || username.length < 3 || username.length > 50) {
      throw createError.badRequest('用户名长度必须在3-50字符之间')
    }

    // 检查用户名格式（只允许字母、数字、下划线、中文）
    const usernameRegex = /^[\w\u4e00-\u9fa5]+$/
    if (!usernameRegex.test(username)) {
      throw createError.badRequest('用户名只能包含字母、数字、下划线和中文字符')
    }
  }

  /**
   * 验证邮箱
   */
  private validateEmail(email: string): void {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      throw createError.badRequest('邮箱格式不正确')
    }
  }

  /**
   * 验证密码
   */
  private validatePassword(password: string): void {
    if (!password || password.length < 8) {
      throw createError.badRequest('密码长度至少8位')
    }

    // 检查密码强度（至少包含字母和数字）
    const hasLetter = /[a-zA-Z]/.test(password)
    const hasNumber = /\d/.test(password)
    
    if (!hasLetter || !hasNumber) {
      throw createError.badRequest('密码必须包含至少一个字母和一个数字')
    }
  }

  /**
   * 从用户对象中排除密码字段
   */
  private excludePassword(user: any): Omit<User, 'password'> {
    const { password, ...userWithoutPassword } = user
    return userWithoutPassword
  }
}
