import { Router } from 'express'
import { z } from 'zod'
import { PrismaClient } from '@prisma/client'
import { authenticateToken } from '../middleware/auth.js'
import type { AuthenticatedRequest } from '../middleware/auth.js'
import { asyncHandler, createError } from '../middleware/error-handler.js'
import type { ApiResponse } from '@shared/index.js'

const router = Router()
const prisma = new PrismaClient()

// AI Provider 验证 Schema
const createProviderSchema = z.object({
  name: z.string().min(1).max(100),
  type: z.string().min(1).max(50),
  baseUrl: z.string().url().max(500),
  apiKey: z.string().max(500).optional(),
  models: z.array(z.object({
    id: z.string(),
    name: z.string(),
    type: z.enum(['text', 'image', 'multimodal'])
  })),
  config: z.record(z.any()).optional(),
  isActive: z.boolean().optional(),
  isDefault: z.boolean().optional()
})

const updateProviderSchema = createProviderSchema.partial()

/**
 * GET /ai-providers
 * 获取所有 AI Providers
 */
router.get('/', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const providers = await prisma.aIProvider.findMany({
    orderBy: [
      { isDefault: 'desc' },
      { isActive: 'desc' },
      { name: 'asc' }
    ]
  })

  const response: ApiResponse = {
    success: true,
    data: providers,
  }

  res.json(response)
}))

/**
 * GET /ai-providers/active
 * 获取激活的 AI Providers
 */
router.get('/active', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const providers = await prisma.aIProvider.findMany({
    where: { isActive: true },
    orderBy: [
      { isDefault: 'desc' },
      { name: 'asc' }
    ]
  })

  const response: ApiResponse = {
    success: true,
    data: providers,
  }

  res.json(response)
}))

/**
 * GET /ai-providers/:id
 * 获取单个 AI Provider
 */
router.get('/:id', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params

  const provider = await prisma.aIProvider.findUnique({
    where: { id }
  })

  if (!provider) {
    throw createError(404, 'AI Provider 不存在')
  }

  const response: ApiResponse = {
    success: true,
    data: provider,
  }

  res.json(response)
}))

/**
 * POST /ai-providers
 * 创建新的 AI Provider
 */
router.post('/', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createProviderSchema.parse(req.body)

  // 检查名称是否已存在
  const existingProvider = await prisma.aIProvider.findUnique({
    where: { name: validatedData.name }
  })

  if (existingProvider) {
    throw createError(400, 'Provider 名称已存在')
  }

  // 如果设置为默认，先取消其他默认设置
  if (validatedData.isDefault) {
    await prisma.aIProvider.updateMany({
      where: { isDefault: true },
      data: { isDefault: false }
    })
  }

  const provider = await prisma.aIProvider.create({
    data: {
      ...validatedData,
      isActive: validatedData.isActive ?? true,
      isDefault: validatedData.isDefault ?? false
    }
  })

  const response: ApiResponse = {
    success: true,
    data: provider,
  }

  res.status(201).json(response)
}))

/**
 * PUT /ai-providers/:id
 * 更新 AI Provider
 */
router.put('/:id', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params
  const validatedData = updateProviderSchema.parse(req.body)

  const existingProvider = await prisma.aIProvider.findUnique({
    where: { id }
  })

  if (!existingProvider) {
    throw createError(404, 'AI Provider 不存在')
  }

  // 检查名称冲突（如果更新了名称）
  if (validatedData.name && validatedData.name !== existingProvider.name) {
    const nameConflict = await prisma.aIProvider.findUnique({
      where: { name: validatedData.name }
    })

    if (nameConflict) {
      throw createError(400, 'Provider 名称已存在')
    }
  }

  // 如果设置为默认，先取消其他默认设置
  if (validatedData.isDefault) {
    await prisma.aIProvider.updateMany({
      where: { 
        isDefault: true,
        id: { not: id }
      },
      data: { isDefault: false }
    })
  }

  const provider = await prisma.aIProvider.update({
    where: { id },
    data: validatedData
  })

  const response: ApiResponse = {
    success: true,
    data: provider,
  }

  res.json(response)
}))

/**
 * DELETE /ai-providers/:id
 * 删除 AI Provider
 */
router.delete('/:id', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params

  const existingProvider = await prisma.aIProvider.findUnique({
    where: { id }
  })

  if (!existingProvider) {
    throw createError(404, 'AI Provider 不存在')
  }

  // 不允许删除默认 Provider
  if (existingProvider.isDefault) {
    throw createError(400, '不能删除默认 AI Provider')
  }

  await prisma.aIProvider.delete({
    where: { id }
  })

  const response: ApiResponse = {
    success: true,
    data: { message: 'AI Provider 删除成功' },
  }

  res.json(response)
}))

/**
 * POST /ai-providers/:id/test
 * 测试 AI Provider 连接
 */
router.post('/:id/test', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params

  const provider = await prisma.aIProvider.findUnique({
    where: { id }
  })

  if (!provider) {
    throw createError(404, 'AI Provider 不存在')
  }

  try {
    // 这里应该实现实际的连接测试逻辑
    // 暂时返回模拟结果
    const testResult = {
      success: true,
      latency: Math.floor(Math.random() * 1000) + 100, // 模拟延迟
      models: provider.models,
      message: '连接测试成功'
    }

    const response: ApiResponse = {
      success: true,
      data: testResult,
    }

    res.json(response)
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: {
        code: 'CONNECTION_TEST_FAILED',
        message: '连接测试失败'
      }
    }
    res.status(500).json(response)
  }
}))

/**
 * POST /ai-providers/:id/set-default
 * 设置默认 AI Provider
 */
router.post('/:id/set-default', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params

  const provider = await prisma.aIProvider.findUnique({
    where: { id }
  })

  if (!provider) {
    throw createError(404, 'AI Provider 不存在')
  }

  // 取消所有默认设置
  await prisma.aIProvider.updateMany({
    where: { isDefault: true },
    data: { isDefault: false }
  })

  // 设置新的默认
  const updatedProvider = await prisma.aIProvider.update({
    where: { id },
    data: { 
      isDefault: true,
      isActive: true // 默认 Provider 必须是激活状态
    }
  })

  const response: ApiResponse = {
    success: true,
    data: updatedProvider,
  }

  res.json(response)
}))

export { router as aiProviderRoutes }
