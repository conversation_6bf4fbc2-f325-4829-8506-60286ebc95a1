import request from 'supertest'
import { vi } from 'vitest'
import express from 'express'
import { PrismaClient } from '@prisma/client'
import { generateTestToken } from '../../utils/test-helpers'

const prisma = new PrismaClient()

// 创建简单的测试 app
const app = express()
app.use(express.json())

// Mock AI Service
const mockAiService = {
  generateText: vi.fn(),
}

vi.mock('../../services/ai/ai-service', () => ({
  aiService: mockAiService
}))

describe('AI Continue Error Handling', () => {
  let authToken: string
  let testUserId: string
  let testNovelId: string
  let testChapterId: string
  let testProviderId: string

  beforeAll(async () => {
    // 创建测试用户
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testaiuser',
        passwordHash: 'hashedpassword',
        isEmailVerified: true,
      },
    })
    testUserId = testUser.id
    authToken = generateTestToken(testUser.id)

    // 创建测试小说
    const testNovel = await prisma.novel.create({
      data: {
        title: 'Test Novel for AI Error',
        description: 'Test novel for AI error handling',
        authorId: testUserId,
      },
    })
    testNovelId = testNovel.id

    // 创建测试章节
    const testChapter = await prisma.chapter.create({
      data: {
        title: 'Test Chapter',
        content: 'This is test content for AI continuation.',
        novelId: testNovelId,
        order: 1,
      },
    })
    testChapterId = testChapter.id

    // 创建测试 AI Provider
    const testProvider = await prisma.aIProvider.create({
      data: {
        name: 'test-provider',
        type: 'gemini',
        config: { apiKey: 'test-key' },
        models: [{ id: 'test-model', name: 'Test Model' }],
        isActive: true,
        isDefault: true,
      },
    })
    testProviderId = testProvider.id
  })

  afterAll(async () => {
    // 清理测试数据
    await prisma.chapter.deleteMany({ where: { novelId: testNovelId } })
    await prisma.novel.deleteMany({ where: { authorId: testUserId } })
    await prisma.aIProvider.deleteMany({ where: { id: testProviderId } })
    await prisma.user.deleteMany({ where: { id: testUserId } })
  })

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('POST /api/ai/continue', () => {
    const validRequest = {
      context: {
        novelId: '',
        chapterId: '',
        previousContent: 'This is the previous content for continuation.',
        characters: [],
        worldSettings: [],
        outline: null,
      },
      length: 'medium' as const,
      style: 'narrative' as const,
      aiConfig: {
        providerId: '',
        modelId: 'test-model',
        temperature: 0.8,
        maxTokens: 600,
      },
    }

    beforeEach(() => {
      validRequest.context.novelId = testNovelId
      validRequest.context.chapterId = testChapterId
      validRequest.aiConfig.providerId = testProviderId
    })

    it('应该返回具体的 AI Provider 不可用错误', async () => {
      // 模拟 AI Service 抛出 Provider 不可用错误
      mockAiService.generateText.mockRejectedValueOnce(
        new Error('AI Provider "gemini" is not available: API key is invalid')
      )

      const response = await request(app)
        .post('/api/ai/continue')
        .set('Authorization', `Bearer ${authToken}`)
        .send(validRequest)

      expect(response.status).toBe(500)
      expect(response.body).toEqual({
        success: false,
        error: {
          code: 'AI_PROVIDER_UNAVAILABLE',
          message: 'AI Provider "gemini" is not available: API key is invalid',
          details: {
            provider: 'gemini',
            reason: 'API key is invalid',
          },
        },
      })
    })

    it('应该返回具体的模型不可用错误', async () => {
      // 模拟 AI Service 抛出模型不可用错误
      mockAiService.generateText.mockRejectedValueOnce(
        new Error('Model "test-model" is not available or has reached rate limit')
      )

      const response = await request(app)
        .post('/api/ai/continue')
        .set('Authorization', `Bearer ${authToken}`)
        .send(validRequest)

      expect(response.status).toBe(500)
      expect(response.body).toEqual({
        success: false,
        error: {
          code: 'AI_MODEL_UNAVAILABLE',
          message: 'Model "test-model" is not available or has reached rate limit',
          details: {
            model: 'test-model',
            reason: 'Rate limit exceeded or model unavailable',
          },
        },
      })
    })

    it('应该返回具体的内容长度错误', async () => {
      // 发送内容过长的请求
      const longContentRequest = {
        ...validRequest,
        context: {
          ...validRequest.context,
          previousContent: 'x'.repeat(50000), // 超长内容
        },
      }

      const response = await request(app)
        .post('/api/ai/continue')
        .set('Authorization', `Bearer ${authToken}`)
        .send(longContentRequest)

      expect(response.status).toBe(400)
      expect(response.body).toEqual({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '输入验证失败',
          details: {
            'context.previousContent': 'String must contain at most 10000 character(s)',
          },
        },
      })
    })

    it('应该返回具体的网络超时错误', async () => {
      // 模拟网络超时错误
      mockAiService.generateText.mockRejectedValueOnce(
        new Error('Request timeout: AI service did not respond within 30 seconds')
      )

      const response = await request(app)
        .post('/api/ai/continue')
        .set('Authorization', `Bearer ${authToken}`)
        .send(validRequest)

      expect(response.status).toBe(500)
      expect(response.body).toEqual({
        success: false,
        error: {
          code: 'AI_REQUEST_TIMEOUT',
          message: 'Request timeout: AI service did not respond within 30 seconds',
          details: {
            timeout: '30 seconds',
            suggestion: 'Please try again or reduce the content length',
          },
        },
      })
    })

    it('应该返回具体的配额超限错误', async () => {
      // 模拟配额超限错误
      mockAiService.generateText.mockRejectedValueOnce(
        new Error('Quota exceeded: You have reached your daily AI generation limit')
      )

      const response = await request(app)
        .post('/api/ai/continue')
        .set('Authorization', `Bearer ${authToken}`)
        .send(validRequest)

      expect(response.status).toBe(429)
      expect(response.body).toEqual({
        success: false,
        error: {
          code: 'AI_QUOTA_EXCEEDED',
          message: 'Quota exceeded: You have reached your daily AI generation limit',
          details: {
            quotaType: 'daily',
            resetTime: expect.any(String),
            suggestion: 'Please try again tomorrow or upgrade your plan',
          },
        },
      })
    })

    it('应该返回具体的内容过滤错误', async () => {
      // 模拟内容过滤错误
      mockAiService.generateText.mockRejectedValueOnce(
        new Error('Content filtered: The generated content violates content policy')
      )

      const response = await request(app)
        .post('/api/ai/continue')
        .set('Authorization', `Bearer ${authToken}`)
        .send(validRequest)

      expect(response.status).toBe(400)
      expect(response.body).toEqual({
        success: false,
        error: {
          code: 'AI_CONTENT_FILTERED',
          message: 'Content filtered: The generated content violates content policy',
          details: {
            reason: 'Content policy violation',
            suggestion: 'Please modify your prompt or previous content',
          },
        },
      })
    })

    it('应该返回通用错误当无法识别具体错误类型时', async () => {
      // 模拟未知错误
      mockAiService.generateText.mockRejectedValueOnce(
        new Error('Unknown error occurred in AI service')
      )

      const response = await request(app)
        .post('/api/ai/continue')
        .set('Authorization', `Bearer ${authToken}`)
        .send(validRequest)

      expect(response.status).toBe(500)
      expect(response.body).toEqual({
        success: false,
        error: {
          code: 'AI_GENERATION_FAILED',
          message: 'Unknown error occurred in AI service',
          details: {
            originalError: 'Unknown error occurred in AI service',
            suggestion: 'Please try again later or contact support if the problem persists',
          },
        },
      })
    })
  })
})
