import { describe, it, expect, vi, beforeEach } from 'vitest'
import request from 'supertest'
import express from 'express'
import { authRoutes } from './auth.js'
import { UserService } from '../services/user.js'
import { jwtUtils } from '../middleware/auth.js'

// Mock dependencies
vi.mock('../services/user.js')
vi.mock('../middleware/auth.js', async () => {
  const actual = await vi.importActual('../middleware/auth.js')
  return {
    ...actual,
    jwtUtils: {
      generateAccessToken: vi.fn(),
      generateRefreshToken: vi.fn(),
      verifyToken: vi.fn(),
    },
  }
})

describe('认证路由', () => {
  let app: express.Application
  let mockUserService: any

  beforeEach(() => {
    vi.clearAllMocks()
    
    // 创建mock UserService
    mockUserService = {
      createUser: vi.fn(),
      authenticateUser: vi.fn(),
      getUserById: vi.fn(),
    }
    vi.mocked(UserService).mockImplementation(() => mockUserService)

    // 创建测试应用
    app = express()
    app.use(express.json())
    app.use('/auth', authRoutes)
  })

  describe('POST /auth/register', () => {
    it('应该成功注册新用户', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
      }

      const createdUser = {
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        avatar: null,
        settings: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      const accessToken = 'access-token-123'
      const refreshToken = 'refresh-token-123'

      mockUserService.createUser.mockResolvedValue(createdUser)
      vi.mocked(jwtUtils.generateAccessToken).mockReturnValue(accessToken)
      vi.mocked(jwtUtils.generateRefreshToken).mockReturnValue(refreshToken)

      const response = await request(app)
        .post('/auth/register')
        .send(userData)
        .expect(201)

      expect(response.body).toEqual({
        success: true,
        data: {
          user: createdUser,
          accessToken,
          refreshToken,
        },
      })

      expect(mockUserService.createUser).toHaveBeenCalledWith(userData)
      expect(jwtUtils.generateAccessToken).toHaveBeenCalledWith({
        userId: 'user-123',
        email: '<EMAIL>',
      })
      expect(jwtUtils.generateRefreshToken).toHaveBeenCalledWith({
        userId: 'user-123',
      })
    })

    it('应该验证必填字段', async () => {
      const invalidData = {
        username: 'testuser',
        // 缺少email和password
      }

      const response = await request(app)
        .post('/auth/register')
        .send(invalidData)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })

    it('应该处理用户服务错误', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
      }

      mockUserService.createUser.mockRejectedValue(new Error('邮箱已被使用'))

      const response = await request(app)
        .post('/auth/register')
        .send(userData)
        .expect(500)

      expect(response.body.success).toBe(false)
    })
  })

  describe('POST /auth/login', () => {
    it('应该成功登录用户', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'password123',
      }

      const user = {
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        avatar: null,
        settings: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      const accessToken = 'access-token-123'
      const refreshToken = 'refresh-token-123'

      mockUserService.authenticateUser.mockResolvedValue(user)
      vi.mocked(jwtUtils.generateAccessToken).mockReturnValue(accessToken)
      vi.mocked(jwtUtils.generateRefreshToken).mockReturnValue(refreshToken)

      const response = await request(app)
        .post('/auth/login')
        .send(credentials)
        .expect(200)

      expect(response.body).toEqual({
        success: true,
        data: {
          user,
          accessToken,
          refreshToken,
        },
      })

      expect(mockUserService.authenticateUser).toHaveBeenCalledWith(credentials)
    })

    it('应该验证登录凭据格式', async () => {
      const invalidCredentials = {
        email: 'invalid-email',
        password: '123', // 太短
      }

      const response = await request(app)
        .post('/auth/login')
        .send(invalidCredentials)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })

    it('应该处理认证失败', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      }

      mockUserService.authenticateUser.mockRejectedValue(new Error('邮箱或密码错误'))

      const response = await request(app)
        .post('/auth/login')
        .send(credentials)
        .expect(500)

      expect(response.body.success).toBe(false)
    })
  })

  describe('POST /auth/refresh', () => {
    it('应该成功刷新访问令牌', async () => {
      const refreshToken = 'valid-refresh-token'
      const decodedToken = {
        userId: 'user-123',
      }

      const user = {
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        avatar: null,
        settings: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      const newAccessToken = 'new-access-token'
      const newRefreshToken = 'new-refresh-token'

      vi.mocked(jwtUtils.verifyToken).mockReturnValue(decodedToken)
      mockUserService.getUserById.mockResolvedValue(user)
      vi.mocked(jwtUtils.generateAccessToken).mockReturnValue(newAccessToken)
      vi.mocked(jwtUtils.generateRefreshToken).mockReturnValue(newRefreshToken)

      const response = await request(app)
        .post('/auth/refresh')
        .send({ refreshToken })
        .expect(200)

      expect(response.body).toEqual({
        success: true,
        data: {
          user,
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
        },
      })

      expect(jwtUtils.verifyToken).toHaveBeenCalledWith(refreshToken)
      expect(mockUserService.getUserById).toHaveBeenCalledWith('user-123')
    })

    it('应该拒绝无效的刷新令牌', async () => {
      const invalidRefreshToken = 'invalid-token'

      vi.mocked(jwtUtils.verifyToken).mockImplementation(() => {
        throw new Error('Invalid token')
      })

      const response = await request(app)
        .post('/auth/refresh')
        .send({ refreshToken: invalidRefreshToken })
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('UNAUTHORIZED')
    })

    it('应该验证刷新令牌格式', async () => {
      const response = await request(app)
        .post('/auth/refresh')
        .send({}) // 缺少refreshToken
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('POST /auth/logout', () => {
    it('应该成功登出用户', async () => {
      const response = await request(app)
        .post('/auth/logout')
        .expect(200)

      expect(response.body).toEqual({
        success: true,
        data: {
          message: '登出成功',
        },
      })
    })
  })

  describe('输入验证', () => {
    it('应该验证邮箱格式', async () => {
      const invalidData = {
        username: 'testuser',
        email: 'invalid-email-format',
        password: 'password123',
      }

      const response = await request(app)
        .post('/auth/register')
        .send(invalidData)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })

    it('应该验证密码长度', async () => {
      const invalidData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: '123', // 太短
      }

      const response = await request(app)
        .post('/auth/register')
        .send(invalidData)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })

    it('应该验证用户名长度', async () => {
      const invalidData = {
        username: 'ab', // 太短
        email: '<EMAIL>',
        password: 'password123',
      }

      const response = await request(app)
        .post('/auth/register')
        .send(invalidData)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })
  })
})
