import { Router } from 'express'
import { z } from 'zod'
import { PrismaClient } from '@prisma/client'
import { UserService } from '../services/user.js'
import { authenticateToken } from '../middleware/auth.js'
import type { AuthenticatedRequest } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/error-handler.js'
import type { ApiResponse } from '@shared/index.js'

const router = Router()
const prisma = new PrismaClient()
const userService = new UserService(prisma)

// 更新用户信息验证Schema
const updateUserSchema = z.object({
  username: z.string().min(3).max(50).optional(),
  email: z.string().email().optional(),
  password: z.string().min(8).optional(),
  avatar: z.string().url().optional(),
  settings: z.record(z.any()).optional(),
})

/**
 * GET /users/profile
 * 获取当前用户信息
 */
router.get('/profile', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = await userService.getUserById(req.user!.userId)

  const response: ApiResponse = {
    success: true,
    data: { user },
  }

  res.json(response)
}))

/**
 * PUT /users/profile
 * 更新当前用户信息
 */
router.put('/profile', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = updateUserSchema.parse(req.body)
  
  const user = await userService.updateUser(req.user!.userId, validatedData)

  const response: ApiResponse = {
    success: true,
    data: { user },
  }

  res.json(response)
}))

/**
 * DELETE /users/profile
 * 删除当前用户账户
 */
router.delete('/profile', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  await userService.deleteUser(req.user!.userId)

  const response: ApiResponse = {
    success: true,
    data: { message: '账户已删除' },
  }

  res.json(response)
}))

export { router as userRoutes }
