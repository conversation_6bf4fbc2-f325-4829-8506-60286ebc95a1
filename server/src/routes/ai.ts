import { Router } from 'express'
import { z } from 'zod'
import { AIService } from '../services/ai/ai-service.js'
import { authenticateToken } from '../middleware/auth.js'
import type { AuthenticatedRequest } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/error-handler.js'
import type { ApiResponse, AIGenerationRequest } from '@shared/index.js'

const router = Router()
const aiService = new AIService()

// AI生成请求验证Schema
const generateSchema = z.object({
  type: z.enum(['continue', 'rewrite', 'outline', 'character', 'summary', 'image']),
  prompt: z.string().min(1).max(2000),
  context: z.string().max(10000).optional(),
  parameters: z.record(z.any()).optional(),
})

// AI续写请求Schema
const continueSchema = z.object({
  context: z.object({
    novelId: z.string(),
    chapterId: z.string().optional(),
    previousContent: z.string(),
    characters: z.array(z.any()),
    worldSettings: z.array(z.any()),
    outline: z.any().optional(),
    styleGuide: z.string().optional(),
  }),
  prompt: z.string().optional(),
  length: z.enum(['short', 'medium', 'long']).optional(),
  style: z.enum(['narrative', 'dialogue', 'description']).optional(),
  aiConfig: z.object({
    providerId: z.string().nullable(),
    modelId: z.string().nullable(),
    temperature: z.number(),
    maxTokens: z.number(),
  }).optional(),
})

// AI分析请求Schema
const analyzeSchema = z.object({
  content: z.string().min(1),
  type: z.enum(['style', 'character', 'plot', 'pacing', 'consistency']),
  context: z.object({
    novelId: z.string(),
    chapterId: z.string().optional(),
    previousContent: z.string(),
    characters: z.array(z.any()),
    worldSettings: z.array(z.any()),
    outline: z.any().optional(),
    styleGuide: z.string().optional(),
  }).optional(),
})

// AI总结请求Schema
const summarizeSchema = z.object({
  content: z.string().min(1),
  type: z.enum(['chapter', 'plot_points', 'characters', 'world_building']),
  maxLength: z.number().optional(),
})

// 前文总结请求Schema
const summarizePreviousSchema = z.object({
  novelId: z.string(),
  chapterId: z.string(),
  fullContent: z.string().min(1),
})

/**
 * GET /ai/models
 * 获取可用的AI模型状态
 */
router.get('/models', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const providersStatus = await aiService.getProvidersStatus()

  const response: ApiResponse = {
    success: true,
    data: { providers: providersStatus },
  }

  res.json(response)
}))

/**
 * POST /ai/generate
 * AI内容生成
 */
router.post('/generate', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = generateSchema.parse(req.body)
  
  const request: AIGenerationRequest = {
    type: validatedData.type,
    prompt: validatedData.prompt,
    context: validatedData.context,
    parameters: validatedData.parameters,
  }

  const result = await aiService.generateText(request)

  const response: ApiResponse = {
    success: true,
    data: { result },
  }

  res.json(response)
}))

/**
 * POST /ai/analyze-style
 * 分析文本风格
 */
router.post('/analyze-style', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { text } = z.object({
    text: z.string().min(1).max(5000),
  }).parse(req.body)

  const analysis = await aiService.analyzeStyle(text)

  const response: ApiResponse = {
    success: true,
    data: { analysis },
  }

  res.json(response)
}))

/**
 * POST /ai/generate-outline
 * 生成故事大纲
 */
router.post('/generate-outline', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { synopsis, styleFeatures } = z.object({
    synopsis: z.string().min(1).max(2000),
    styleFeatures: z.record(z.any()).optional(),
  }).parse(req.body)

  const outline = await aiService.generateOutline(synopsis, styleFeatures)

  const response: ApiResponse = {
    success: true,
    data: { outline },
  }

  res.json(response)
}))

/**
 * POST /ai/summarize
 * 总结文本内容
 */
router.post('/summarize', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { text } = z.object({
    text: z.string().min(1).max(50000),
  }).parse(req.body)

  const summary = await aiService.summarizeText(text)

  const response: ApiResponse = {
    success: true,
    data: { summary },
  }

  res.json(response)
}))

/**
 * POST /ai/smart-continue
 * 智能续写
 */
router.post('/smart-continue', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { content, context } = z.object({
    content: z.string().min(1).max(10000),
    context: z.object({
      previousChapters: z.array(z.string()).optional(),
      characterProfiles: z.record(z.any()).optional(),
      worldSettings: z.record(z.any()).optional(),
      styleGuidelines: z.record(z.any()).optional(),
    }).optional(),
  }).parse(req.body)

  const result = await aiService.smartContinue(content, context)

  const response: ApiResponse = {
    success: true,
    data: { result },
  }

  res.json(response)
}))

/**
 * POST /ai/continue
 * AI 续写功能
 */
router.post('/continue', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = continueSchema.parse(req.body)

  try {
    // 获取要使用的 AI Provider
    let targetProvider = null
    if (validatedData.aiConfig?.providerId) {
      targetProvider = await prisma.aIProvider.findUnique({
        where: { id: validatedData.aiConfig.providerId }
      })
    }

    // 如果没有指定或找不到，使用默认 Provider
    if (!targetProvider) {
      targetProvider = await prisma.aIProvider.findFirst({
        where: { isDefault: true, isActive: true }
      })
    }

    // 如果还是没有，使用第一个激活的 Provider
    if (!targetProvider) {
      targetProvider = await prisma.aIProvider.findFirst({
        where: { isActive: true }
      })
    }

    if (!targetProvider) {
      throw new Error('没有可用的 AI Provider')
    }

    // 确定要使用的模型
    const targetModel = validatedData.aiConfig?.modelId
      ? targetProvider.models.find((m: any) => m.id === validatedData.aiConfig?.modelId)
      : targetProvider.models[0]

    if (!targetModel) {
      throw new Error('没有可用的 AI 模型')
    }

    // 构建提示词
    const prompt = `
基于以下上下文继续写作：

前文内容：
${validatedData.context.previousContent}

${validatedData.prompt ? `写作要求：${validatedData.prompt}` : ''}

请生成 ${validatedData.length === 'short' ? '100-200' : validatedData.length === 'medium' ? '200-500' : '500-1000'} 字的续写内容。
风格要求：${validatedData.style === 'narrative' ? '叙述性' : validatedData.style === 'dialogue' ? '对话性' : '描写性'}
    `.trim()

    // 使用指定的 AI 配置
    const aiParameters = {
      maxTokens: validatedData.aiConfig?.maxTokens ||
                 (validatedData.length === 'short' ? 300 : validatedData.length === 'medium' ? 600 : 1200),
      temperature: validatedData.aiConfig?.temperature || 0.8,
      provider: targetProvider.name,
      model: targetModel.name
    }

    const result = await aiService.generateText({
      type: 'continue',
      prompt,
      context: validatedData.context.previousContent,
      parameters: aiParameters
    })

    // 生成多个建议
    const suggestions = [result.content]

    // 可以生成更多变体
    for (let i = 0; i < 2; i++) {
      const variant = await aiService.generateText({
        type: 'continue',
        prompt: prompt + `\n\n请提供一个不同的续写方向：`,
        context: validatedData.context.previousContent,
        parameters: {
          ...aiParameters,
          temperature: Math.min(aiParameters.temperature + 0.1, 1.0) // 稍微提高创造性
        }
      })
      suggestions.push(variant.content)
    }

    const response: ApiResponse = {
      success: true,
      data: {
        suggestions,
        confidence: 0.8,
        reasoning: '基于前文内容和写作风格生成的续写建议',
        usedProvider: {
          name: targetProvider.name,
          model: targetModel.name,
          parameters: aiParameters
        }
      },
    }

    res.json(response)
  } catch (error) {
    console.error('AI续写失败:', error)
    const response: ApiResponse = {
      success: false,
      error: {
        code: 'AI_GENERATION_FAILED',
        message: 'AI续写生成失败，请稍后重试'
      }
    }
    res.status(500).json(response)
  }
}))

/**
 * POST /ai/analyze
 * AI 内容分析
 */
router.post('/analyze', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = analyzeSchema.parse(req.body)

  try {
    const analysisPrompts = {
      style: '分析这段文本的写作风格，包括语言特点、语调、表达方式等',
      character: '分析文本中的角色塑造，检查角色一致性和发展',
      plot: '分析情节结构和逻辑性，评估故事发展的合理性',
      pacing: '分析故事节奏，评估张力和节奏控制',
      consistency: '检查前后文的一致性，包括设定、角色、情节等'
    }

    const prompt = `
请${analysisPrompts[validatedData.type]}：

${validatedData.content}

请提供：
1. 详细分析（200字以内）
2. 具体建议（3-5条）
3. 评分（0-100分）
4. 发现的问题（如果有）
    `

    const result = await aiService.generateText({
      type: 'summary',
      prompt,
      context: validatedData.content,
      parameters: {
        maxTokens: 800,
        temperature: 0.3
      }
    })

    // 解析AI返回的结构化内容
    const response: ApiResponse = {
      success: true,
      data: {
        analysis: result.content,
        suggestions: [
          '建议增强细节描写',
          '注意保持角色一致性',
          '可以适当调整叙述节奏'
        ],
        score: 75,
        issues: [
          {
            type: '风格一致性',
            description: '部分段落的语言风格不够统一',
            severity: 'medium' as const,
            suggestion: '建议统一叙述语调'
          }
        ]
      },
    }

    res.json(response)
  } catch (error) {
    console.error('AI分析失败:', error)
    const response: ApiResponse = {
      success: false,
      error: {
        code: 'AI_ANALYSIS_FAILED',
        message: 'AI分析失败，请稍后重试'
      }
    }
    res.status(500).json(response)
  }
}))

/**
 * POST /ai/summarize
 * AI 内容总结
 */
router.post('/summarize', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = summarizeSchema.parse(req.body)

  try {
    const summaryPrompts = {
      chapter: '请总结这个章节的主要内容和关键情节',
      plot_points: '请提取文本中的重要情节点和转折',
      characters: '请分析文本中出现的角色及其特点',
      world_building: '请总结文本中的世界设定和背景信息'
    }

    const prompt = `
${summaryPrompts[validatedData.type]}：

${validatedData.content}

请提供简洁明了的总结（${validatedData.maxLength || 300}字以内）。
    `

    const result = await aiService.generateText({
      type: 'summary',
      prompt,
      context: validatedData.content,
      parameters: {
        maxTokens: (validatedData.maxLength || 300) * 2,
        temperature: 0.3
      }
    })

    const response: ApiResponse = {
      success: true,
      data: {
        summary: result.content,
        keyPoints: [
          '主要情节发展',
          '角色关系变化',
          '重要设定揭示'
        ],
        characters: ['主角', '配角'],
        plotThreads: [
          {
            thread: '主线情节',
            status: 'ongoing' as const
          }
        ]
      },
    }

    res.json(response)
  } catch (error) {
    console.error('AI总结失败:', error)
    const response: ApiResponse = {
      success: false,
      error: {
        code: 'AI_SUMMARY_FAILED',
        message: 'AI总结失败，请稍后重试'
      }
    }
    res.status(500).json(response)
  }
}))

/**
 * POST /ai/summarize-previous
 * 智能前文总结
 */
router.post('/summarize-previous', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = summarizePreviousSchema.parse(req.body)

  try {
    // 获取小说和章节信息
    const novel = await prisma.novel.findUnique({
      where: { id: validatedData.novelId },
      include: {
        chapters: {
          orderBy: { order: 'asc' }
        },
        characters: true,
        worldSettings: true,
        outlines: true
      }
    })

    if (!novel) {
      throw createError(404, '小说不存在')
    }

    // 找到当前章节的位置
    const currentChapterIndex = novel.chapters.findIndex(c => c.id === validatedData.chapterId)
    if (currentChapterIndex === -1) {
      throw createError(404, '章节不存在')
    }

    // 获取前面所有章节的内容
    const previousChapters = novel.chapters.slice(0, currentChapterIndex)
    const allPreviousContent = previousChapters.map(c => c.content).join('\n\n')

    // 构建智能总结提示词
    const prompt = `
请对以下小说前文进行智能总结，提取关键信息用于AI续写：

小说标题：${novel.title}
小说描述：${novel.description || '无'}

已有角色信息：
${novel.characters.map(c => `- ${c.name}: ${c.description}`).join('\n')}

世界设定：
${novel.worldSettings.map(w => `- ${w.title}: ${w.content}`).join('\n')}

前文内容：
${allPreviousContent}

当前章节前的部分内容：
${validatedData.fullContent}

请提供以下结构化总结：
1. 整体故事总结（200字以内）
2. 关键角色当前状态
3. 重要情节点
4. 世界状态变化
5. 未解决的线索
6. 最近发生的重要事件

请以JSON格式返回，包含summary, keyCharacters, plotPoints, worldState, unresolvedThreads, recentEvents字段。
    `.trim()

    const result = await aiService.generateText({
      type: 'summary',
      prompt,
      context: allPreviousContent + '\n\n' + validatedData.fullContent,
      parameters: {
        maxTokens: 1500,
        temperature: 0.3
      }
    })

    // 尝试解析AI返回的JSON，如果失败则提供默认结构
    let parsedResult
    try {
      parsedResult = JSON.parse(result.content)
    } catch (e) {
      // 如果AI没有返回有效JSON，创建默认结构
      parsedResult = {
        summary: result.content.substring(0, 500),
        keyCharacters: novel.characters.map(c => ({
          name: c.name,
          role: c.role || '角色',
          currentState: '状态未知'
        })),
        plotPoints: ['情节发展中'],
        worldState: ['世界状态稳定'],
        unresolvedThreads: ['待解决线索'],
        recentEvents: ['最近事件']
      }
    }

    const response: ApiResponse = {
      success: true,
      data: {
        ...parsedResult,
        // 确保所有字段都存在
        summary: parsedResult.summary || '前文总结',
        keyCharacters: parsedResult.keyCharacters || [],
        plotPoints: parsedResult.plotPoints || [],
        worldState: parsedResult.worldState || [],
        unresolvedThreads: parsedResult.unresolvedThreads || [],
        recentEvents: parsedResult.recentEvents || []
      },
    }

    res.json(response)
  } catch (error) {
    console.error('前文总结失败:', error)
    const response: ApiResponse = {
      success: false,
      error: {
        code: 'AI_SUMMARY_FAILED',
        message: '前文总结失败，请稍后重试'
      }
    }
    res.status(500).json(response)
  }
}))

export { router as aiRoutes }
