import type { Router as ExpressRouter } from 'express'
import { Router } from 'express'
import { z } from 'zod'
import { PrismaClient } from '@prisma/client'
import { UserService } from '../services/user.js'
import { jwtUtils } from '../middleware/auth.js'
import { asyncHandler, createError } from '../middleware/error-handler.js'
import type { ApiResponse } from '@shared/index.js'

const router = Router()
const prisma = new PrismaClient()
const userService = new UserService(prisma)

// 注册请求验证Schema
const registerSchema = z.object({
  username: z.string().min(3, '用户名长度至少3位').max(50, '用户名长度最多50位'),
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(8, '密码长度至少8位'),
})

// 登录请求验证Schema
const loginSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(1, '密码不能为空'),
})

// 刷新令牌请求验证Schema
const refreshSchema = z.object({
  refreshToken: z.string().min(1, '刷新令牌不能为空'),
})

/**
 * POST /auth/register
 * 用户注册
 */
router.post('/register', asyncHandler(async (req, res) => {
  // 验证请求数据
  const validatedData = registerSchema.parse(req.body)

  // 创建用户
  const user = await userService.createUser(validatedData)

  // 生成JWT令牌
  const accessToken = jwtUtils.generateAccessToken({
    userId: user.id,
    email: user.email,
  })

  const refreshToken = jwtUtils.generateRefreshToken({
    userId: user.id,
  })

  const response: ApiResponse = {
    success: true,
    data: {
      user,
      accessToken,
      refreshToken,
    },
  }

  res.status(201).json(response)
}))

/**
 * POST /auth/login
 * 用户登录
 */
router.post('/login', asyncHandler(async (req, res) => {
  // 验证请求数据
  const validatedData = loginSchema.parse(req.body)

  // 验证用户凭据
  const user = await userService.authenticateUser(validatedData)

  // 生成JWT令牌
  const accessToken = jwtUtils.generateAccessToken({
    userId: user.id,
    email: user.email,
  })

  const refreshToken = jwtUtils.generateRefreshToken({
    userId: user.id,
  })

  const response: ApiResponse = {
    success: true,
    data: {
      user,
      accessToken,
      refreshToken,
    },
  }

  res.json(response)
}))

/**
 * POST /auth/refresh
 * 刷新访问令牌
 */
router.post('/refresh', asyncHandler(async (req, res) => {
  // 验证请求数据
  const { refreshToken } = refreshSchema.parse(req.body)

  try {
    // 验证刷新令牌
    const decoded = jwtUtils.verifyToken(refreshToken) as any

    if (!decoded.userId) {
      throw createError.unauthorized('无效的刷新令牌')
    }

    // 获取用户信息
    const user = await userService.getUserById(decoded.userId)

    // 生成新的令牌
    const newAccessToken = jwtUtils.generateAccessToken({
      userId: user.id,
      email: user.email,
    })

    const newRefreshToken = jwtUtils.generateRefreshToken({
      userId: user.id,
    })

    const response: ApiResponse = {
      success: true,
      data: {
        user,
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
      },
    }

    res.json(response)
  } catch (error: any) {
    if (error.name === 'TokenExpiredError') {
      throw createError.unauthorized('刷新令牌已过期')
    } else if (error.name === 'JsonWebTokenError') {
      throw createError.unauthorized('无效的刷新令牌')
    }
    throw error
  }
}))

/**
 * POST /auth/logout
 * 用户登出
 */
router.post('/logout', asyncHandler(async (req, res) => {
  // 注意：在无状态JWT系统中，登出主要是客户端删除令牌
  // 如果需要服务端令牌黑名单，可以在这里实现

  const response: ApiResponse = {
    success: true,
    data: {
      message: '登出成功',
    },
  }

  res.json(response)
}))

/**
 * POST /auth/forgot-password
 * 忘记密码（发送重置邮件）
 */
router.post('/forgot-password', asyncHandler(async (req, res) => {
  const { email } = z.object({
    email: z.string().email('邮箱格式不正确'),
  }).parse(req.body)

  // 检查用户是否存在
  const user = await userService.getUserByEmail(email)
  
  if (!user) {
    // 为了安全，即使用户不存在也返回成功消息
    const response: ApiResponse = {
      success: true,
      data: {
        message: '如果该邮箱已注册，您将收到密码重置邮件',
      },
    }
    res.json(response)
    return
  }

  // TODO: 实现邮件发送功能
  // 1. 生成重置令牌
  // 2. 存储令牌（Redis或数据库）
  // 3. 发送重置邮件

  const response: ApiResponse = {
    success: true,
    data: {
      message: '如果该邮箱已注册，您将收到密码重置邮件',
    },
  }

  res.json(response)
}))

/**
 * POST /auth/reset-password
 * 重置密码
 */
router.post('/reset-password', asyncHandler(async (req, res) => {
  const { token, newPassword } = z.object({
    token: z.string().min(1, '重置令牌不能为空'),
    newPassword: z.string().min(8, '新密码长度至少8位'),
  }).parse(req.body)

  // TODO: 实现密码重置功能
  // 1. 验证重置令牌
  // 2. 更新用户密码
  // 3. 删除重置令牌

  throw createError.serviceUnavailable('密码重置功能暂未实现')
}))

export { router as authRoutes }
