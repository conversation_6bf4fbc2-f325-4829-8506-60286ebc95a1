# 🔧 环境变量配置指南

## 📋 DATABASE_URL 配置

### 格式说明
```
postgresql://[username]:[password]@[host]:[port]/[database_name]?[options]
```

### 🎯 不同环境的配置

#### 1. 开发环境（推荐）
使用开发脚本启动的 Docker 数据库：
```bash
DATABASE_URL="postgresql://postgres:password@localhost:5432/augment_writer_dev"
```

#### 2. 本地 PostgreSQL
如果你本地安装了 PostgreSQL：
```bash
DATABASE_URL="postgresql://postgres:your_password@localhost:5432/augment_writer"
```

#### 3. 生产环境
```bash
DATABASE_URL="postgresql://augment_user:<EMAIL>:5432/augment_writer_prod?sslmode=require"
```

#### 4. 云服务示例
```bash
# Supabase
DATABASE_URL="postgresql://postgres.xxx:<EMAIL>:5432/postgres"

# Railway
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/railway"

# Render
DATABASE_URL="postgresql://user:<EMAIL>/database_name"
```

## 🔧 配置步骤

### 1. 复制环境变量文件
```bash
# 根目录
cp .env.example .env

# 服务器目录（Prisma 需要）
cp .env.example server/.env
```

### 2. 编辑环境变量
```bash
# 编辑根目录的 .env
nano .env

# 编辑服务器的 .env
nano server/.env
```

### 3. 启动数据库
```bash
# 启动 Docker 数据库
./scripts/dev.sh db

# 或者使用 Docker Compose
docker-compose -f docker-compose.dev.yml up -d postgres-dev
```

### 4. 测试连接
```bash
# 验证 Prisma 配置
cd server && npx prisma validate

# 生成 Prisma 客户端
cd server && npm run db:generate

# 运行数据库迁移
cd server && npm run db:migrate
```

## 🧪 验证配置

### 检查数据库连接
```bash
# 使用 psql 直接连接
psql "postgresql://postgres:password@localhost:5432/augment_writer_dev"

# 或者使用 Prisma Studio
cd server && npm run db:studio
```

### 检查服务状态
```bash
# 查看所有服务状态
./scripts/dev.sh status

# 启动完整开发环境
./scripts/dev.sh start
```

## ⚠️ 常见问题

### 1. Prisma 找不到 DATABASE_URL
**错误**: `Environment variable not found: DATABASE_URL`

**解决方案**:
- 确保 `server/.env` 文件存在
- 检查环境变量格式是否正确
- 使用 `dotenv -e .env -- prisma validate` 测试

### 2. 数据库连接失败
**错误**: `Can't reach database server`

**解决方案**:
- 检查数据库是否启动：`./scripts/dev.sh status`
- 检查端口是否正确：默认 PostgreSQL 使用 5432
- 检查密码是否与 Docker 配置匹配

### 3. 端口冲突
**错误**: `port is already allocated`

**解决方案**:
- 使用智能启动脚本：`./scripts/dev.sh start`
- 或者停止现有服务：`./scripts/dev.sh stop`

### 4. 权限问题
**错误**: `permission denied for database`

**解决方案**:
- 检查用户名和密码
- 确保数据库用户有足够权限
- 重新创建数据库：`npm run db:reset`

## 🔒 安全建议

### 开发环境
- 使用简单密码（如 `password`）
- 不要提交 `.env` 文件到版本控制

### 生产环境
- 使用强密码（至少 16 位，包含特殊字符）
- 启用 SSL：`?sslmode=require`
- 使用环境变量管理工具
- 定期轮换密码

## 📚 相关文档

- [Prisma 连接字符串文档](https://www.prisma.io/docs/reference/database-reference/connection-urls)
- [PostgreSQL 连接参数](https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-PARAMKEYWORDS)
- [Docker Compose 环境变量](https://docs.docker.com/compose/environment-variables/)
