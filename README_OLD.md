# AugmentWriter - AI辅助小说创作平台

一个集成了Gemini、Ollama和ComfyUI的智能写作平台，帮助作者创作高质量的小说作品。

## ✨ 核心特性

- 🤖 **多AI模型支持** - 集成Gemini API、Ollama本地模型和ComfyUI图像生成
- 📝 **智能写作辅助** - 基于风格样本的个性化续写建议
- 📋 **智能大纲生成** - AI分析概要自动生成可调整的故事大纲
- 🧠 **上下文记忆** - 自动总结前文，保持故事连贯性
- 🎨 **插图生成** - 根据文本描述自动生成场景和角色插图
- 👥 **角色管理** - 智能角色设定和关系管理
- 🌍 **世界观构建** - 系统化的世界设定管理

## 🏗️ 技术架构

```
Frontend (React + TypeScript)
├── 富文本编辑器 (Tiptap)
├── 实时协作 (Socket.io)
└── 响应式UI (Tailwind CSS)

Backend (Node.js + Express)
├── RESTful API
├── WebSocket 服务
└── AI服务集成层

AI Services
├── Gemini API (主力文本生成)
├── Ollama (本地模型备用)
└── ComfyUI (图像生成)

Database
├── PostgreSQL (主数据库)
├── Redis (缓存)
└── MinIO/S3 (文件存储)
```

## 🚀 快速开始

### 环境要求

- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Ollama (可选)
- ComfyUI (可选)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd augment-writer
```

2. **安装依赖**
```bash
npm run setup
```

3. **环境配置**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和API密钥
```

4. **数据库初始化**
```bash
cd server
npm run db:migrate
npm run db:seed
```

5. **启动开发服务器**
```bash
npm run dev
```

访问 http://localhost:3000 开始使用

## 📁 项目结构

```
augment-writer/
├── client/                 # React前端应用
│   ├── src/
│   │   ├── components/     # 可复用组件
│   │   ├── pages/         # 页面组件
│   │   ├── hooks/         # 自定义Hooks
│   │   ├── services/      # API服务
│   │   ├── stores/        # 状态管理
│   │   └── utils/         # 工具函数
│   └── public/            # 静态资源
├── server/                # Node.js后端应用
│   ├── src/
│   │   ├── controllers/   # 控制器
│   │   ├── models/        # 数据模型
│   │   ├── services/      # 业务逻辑
│   │   ├── routes/        # 路由定义
│   │   ├── middleware/    # 中间件
│   │   └── utils/         # 工具函数
│   └── prisma/            # 数据库Schema
├── shared/                # 共享类型定义
└── docs/                  # 项目文档
```

## 🔧 配置说明

### AI服务配置

1. **Gemini API**
```env
GEMINI_API_KEY=your_gemini_api_key
GEMINI_MODEL_FAST=gemini-2.0-flash
GEMINI_MODEL_PRO=gemini-2.5-pro
```

2. **Ollama配置**
```env
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2
```

3. **ComfyUI配置**
```env
COMFYUI_BASE_URL=http://localhost:8188
COMFYUI_WORKFLOW_PATH=./workflows
```

## 📖 使用指南

### 创建新小说项目

1. 输入小说概要和基本信息
2. 提供风格样本片段（可选）
3. AI分析并生成初始大纲
4. 调整大纲结构
5. 开始章节创作

### AI辅助写作

- **续写建议**: 在编辑器中按 `Ctrl+Space` 获取AI续写建议
- **风格保持**: AI会根据已有内容保持一致的写作风格
- **实时修改**: 选中文本后可获取改进建议

### 插图生成

1. 选择需要插图的段落
2. 描述场景或角色外观
3. AI自动生成对应插图
4. 可调整生成参数重新生成

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
