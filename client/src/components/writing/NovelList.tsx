import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import { PlusIcon, BookOpenIcon, ClockIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline'
import { novelApi } from '../../services/api'
import { useEditorStore } from '../../stores/editor'
import toast from 'react-hot-toast'

interface Novel {
  id: string
  title: string
  description?: string
  status: string
  wordCount: number
  targetWordCount?: number
  createdAt: string
  updatedAt: string
}

interface CreateNovelModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: { title: string; description?: string; genre?: string; targetWordCount?: number }) => void
}

function CreateNovelModal({ isOpen, onClose, onSubmit }: CreateNovelModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    genre: '',
    targetWordCount: ''
  })

  if (!isOpen) return null

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.title.trim()) {
      toast.error('请输入小说标题')
      return
    }

    onSubmit({
      title: formData.title,
      description: formData.description || undefined,
      genre: formData.genre || undefined,
      targetWordCount: formData.targetWordCount ? parseInt(formData.targetWordCount) : undefined
    })

    setFormData({ title: '', description: '', genre: '', targetWordCount: '' })
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-semibold mb-4">创建新小说</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              标题 *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入小说标题"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              简介
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={3}
              placeholder="简要描述你的小说"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              类型
            </label>
            <select
              value={formData.genre}
              onChange={(e) => setFormData({ ...formData, genre: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">选择类型</option>
              <option value="fantasy">奇幻</option>
              <option value="romance">言情</option>
              <option value="mystery">悬疑</option>
              <option value="sci-fi">科幻</option>
              <option value="historical">历史</option>
              <option value="contemporary">现代</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              目标字数
            </label>
            <input
              type="number"
              value={formData.targetWordCount}
              onChange={(e) => setFormData({ ...formData, targetWordCount: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="例如: 100000"
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              创建
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

function NovelCard({ novel, onEdit, onDelete }: { 
  novel: Novel
  onEdit: (novel: Novel) => void
  onDelete: (novelId: string) => void 
}) {
  const progress = novel.targetWordCount 
    ? Math.min((novel.wordCount / novel.targetWordCount) * 100, 100)
    : 0

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      draft: '草稿',
      writing: '写作中',
      completed: '已完成',
      published: '已发布'
    }
    return statusMap[status] || status
  }

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      draft: 'bg-gray-100 text-gray-800',
      writing: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800',
      published: 'bg-purple-100 text-purple-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
          {novel.title}
        </h3>
        <div className="flex space-x-2">
          <button
            onClick={() => onEdit(novel)}
            className="p-1 text-gray-400 hover:text-blue-600"
            title="编辑"
          >
            <PencilIcon className="w-4 h-4" />
          </button>
          <button
            onClick={() => onDelete(novel.id)}
            className="p-1 text-gray-400 hover:text-red-600"
            title="删除"
          >
            <TrashIcon className="w-4 h-4" />
          </button>
        </div>
      </div>

      {novel.description && (
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
          {novel.description}
        </p>
      )}

      <div className="flex items-center justify-between mb-4">
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(novel.status)}`}>
          {getStatusText(novel.status)}
        </span>
        <div className="flex items-center text-gray-500 text-sm">
          <ClockIcon className="w-4 h-4 mr-1" />
          {formatDate(novel.updatedAt)}
        </div>
      </div>

      {novel.targetWordCount && (
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-600 mb-1">
            <span>进度</span>
            <span>{novel.wordCount.toLocaleString()} / {novel.targetWordCount.toLocaleString()} 字</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="text-right text-xs text-gray-500 mt-1">
            {progress.toFixed(1)}%
          </div>
        </div>
      )}

      <button
        onClick={() => onEdit(novel)}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center"
      >
        <BookOpenIcon className="w-4 h-4 mr-2" />
        继续写作
      </button>
    </div>
  )
}

export default function NovelList() {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const queryClient = useQueryClient()
  const navigate = useNavigate()
  const setCurrentNovel = useEditorStore(state => state.setCurrentNovel)

  // 获取小说列表
  const { data: novelsResponse, isLoading, error } = useQuery({
    queryKey: ['novels'],
    queryFn: () => novelApi.getNovels(),
  })

  const novels = Array.isArray(novelsResponse?.data) ? novelsResponse.data : []

  // 调试日志
  console.log('NovelList - novelsResponse:', novelsResponse)
  console.log('NovelList - novels:', novels)
  console.log('NovelList - isLoading:', isLoading)
  console.log('NovelList - error:', error)

  // 创建小说
  const createMutation = useMutation({
    mutationFn: novelApi.createNovel,
    onSuccess: (data) => {
      console.log('创建小说成功:', data)
      queryClient.invalidateQueries({ queryKey: ['novels'] })
      toast.success('小说创建成功')
    },
    onError: (error) => {
      console.error('创建小说失败:', error)
      toast.error('创建失败，请重试')
    }
  })

  // 删除小说
  const deleteMutation = useMutation({
    mutationFn: novelApi.deleteNovel,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['novels'] })
      toast.success('小说删除成功')
    },
    onError: () => {
      toast.error('删除失败，请重试')
    }
  })

  const handleCreateNovel = (data: any) => {
    createMutation.mutate(data)
  }

  const handleEditNovel = (novel: Novel) => {
    setCurrentNovel(novel)
    navigate(`/novel/${novel.id}`)
  }

  const handleDeleteNovel = (novelId: string) => {
    if (window.confirm('确定要删除这部小说吗？此操作不可恢复。')) {
      deleteMutation.mutate(novelId)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-500">加载失败，请刷新重试</div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">我的小说</h1>
          <p className="text-gray-600 mt-2">管理你的创作项目</p>
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
        >
          <PlusIcon className="w-5 h-5 mr-2" />
          创建新小说
        </button>
      </div>

      {novels.length === 0 ? (
        <div className="text-center py-12">
          <BookOpenIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">还没有小说</h3>
          <p className="text-gray-500 mb-6">开始你的第一部作品吧</p>
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700"
          >
            创建第一部小说
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {novels.map((novel: Novel) => (
            <NovelCard
              key={novel.id}
              novel={novel}
              onEdit={handleEditNovel}
              onDelete={handleDeleteNovel}
            />
          ))}
        </div>
      )}

      <CreateNovelModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateNovel}
      />
    </div>
  )
}
