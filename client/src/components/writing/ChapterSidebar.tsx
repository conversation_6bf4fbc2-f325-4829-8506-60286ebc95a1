import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { 
  PlusIcon, 
  DocumentTextIcon, 
  EllipsisVerticalIcon,
  PencilIcon,
  TrashIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'
import { novelApi } from '../../services/api'
import { useEditorStore } from '../../stores/editor'
import toast from 'react-hot-toast'

interface Chapter {
  id: string
  title: string
  content: string
  status: string
  wordCount: number
  orderIndex: number
  updatedAt: string
}

interface ChapterSidebarProps {
  novelId: string
}

interface ChapterItemProps {
  chapter: Chapter
  isActive: boolean
  onSelect: (chapter: Chapter) => void
  onRename: (chapterId: string, newTitle: string) => void
  onDelete: (chapterId: string) => void
}

function ChapterItem({ chapter, isActive, onSelect, onRename, onDelete }: ChapterItemProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editTitle, setEditTitle] = useState(chapter.title)
  const [showMenu, setShowMenu] = useState(false)

  const handleSaveEdit = () => {
    if (editTitle.trim() && editTitle !== chapter.title) {
      onRename(chapter.id, editTitle.trim())
    }
    setIsEditing(false)
    setShowMenu(false)
  }

  const handleCancelEdit = () => {
    setEditTitle(chapter.title)
    setIsEditing(false)
    setShowMenu(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600'
      case 'writing': return 'text-blue-600'
      case 'draft': return 'text-gray-500'
      default: return 'text-gray-500'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '已完成'
      case 'writing': return '写作中'
      case 'draft': return '草稿'
      default: return '草稿'
    }
  }

  return (
    <div 
      className={`
        relative group p-3 rounded-lg cursor-pointer transition-colors
        ${isActive 
          ? 'bg-blue-50 border border-blue-200' 
          : 'hover:bg-gray-50 border border-transparent'
        }
      `}
      onClick={() => !isEditing && onSelect(chapter)}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          {isEditing ? (
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleSaveEdit()
                  if (e.key === 'Escape') handleCancelEdit()
                }}
                autoFocus
              />
              <button
                onClick={handleSaveEdit}
                className="p-1 text-green-600 hover:bg-green-100 rounded"
              >
                <CheckIcon className="w-4 h-4" />
              </button>
              <button
                onClick={handleCancelEdit}
                className="p-1 text-red-600 hover:bg-red-100 rounded"
              >
                <XMarkIcon className="w-4 h-4" />
              </button>
            </div>
          ) : (
            <>
              <div className="flex items-center space-x-2">
                <DocumentTextIcon className="w-4 h-4 text-gray-400 flex-shrink-0" />
                <h3 className="text-sm font-medium text-gray-900 truncate">
                  {chapter.title}
                </h3>
              </div>
              
              <div className="mt-1 flex items-center justify-between text-xs text-gray-500">
                <span className={getStatusColor(chapter.status)}>
                  {getStatusText(chapter.status)}
                </span>
                <span>{chapter.wordCount.toLocaleString()} 字</span>
              </div>
              
              <div className="mt-1 text-xs text-gray-400">
                {new Date(chapter.updatedAt).toLocaleDateString('zh-CN')}
              </div>
            </>
          )}
        </div>

        {!isEditing && (
          <div className="relative">
            <button
              onClick={(e) => {
                e.stopPropagation()
                setShowMenu(!showMenu)
              }}
              className="p-1 text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <EllipsisVerticalIcon className="w-4 h-4" />
            </button>

            {showMenu && (
              <div className="absolute right-0 top-6 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    setIsEditing(true)
                    setShowMenu(false)
                  }}
                  className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                >
                  <PencilIcon className="w-4 h-4 mr-2" />
                  重命名
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onDelete(chapter.id)
                    setShowMenu(false)
                  }}
                  className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center"
                >
                  <TrashIcon className="w-4 h-4 mr-2" />
                  删除
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 点击外部关闭菜单 */}
      {showMenu && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={() => setShowMenu(false)}
        />
      )}
    </div>
  )
}

export function ChapterSidebar({ novelId }: ChapterSidebarProps) {
  const [isCreating, setIsCreating] = useState(false)
  const [newChapterTitle, setNewChapterTitle] = useState('')
  
  const { 
    currentChapter, 
    chapters, 
    setChapters, 
    switchToChapter, 
    createChapter, 
    deleteChapter 
  } = useEditorStore()

  // 获取章节列表
  const { data: chaptersResponse, refetch } = useQuery({
    queryKey: ['chapters', novelId],
    queryFn: () => novelApi.getChapters(novelId),
    enabled: !!novelId,
  })

  // 处理章节数据
  React.useEffect(() => {
    if (chaptersResponse?.data) {
      setChapters(chaptersResponse.data)
    }
  }, [chaptersResponse, setChapters])

  const handleCreateChapter = async () => {
    if (!newChapterTitle.trim()) {
      toast.error('请输入章节标题')
      return
    }

    const chapter = await createChapter(newChapterTitle.trim())
    if (chapter) {
      setNewChapterTitle('')
      setIsCreating(false)
      refetch()
    }
  }

  const handleRenameChapter = async (_chapterId: string, _newTitle: string) => {
    try {
      // TODO: 实现重命名 API 调用
      toast.success('章节重命名成功')
      refetch()
    } catch (error) {
      toast.error('重命名失败')
    }
  }

  const handleDeleteChapter = async (chapterId: string) => {
    if (window.confirm('确定要删除这个章节吗？此操作不可恢复。')) {
      await deleteChapter(chapterId)
      refetch()
    }
  }

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col h-full">
      {/* 头部 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">章节</h2>
          <button
            onClick={() => setIsCreating(true)}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md"
            title="添加章节"
          >
            <PlusIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* 章节列表 */}
      <div className="flex-1 overflow-auto p-4 space-y-2">
        {/* 创建新章节 */}
        {isCreating && (
          <div className="p-3 border border-gray-200 rounded-lg bg-gray-50">
            <input
              type="text"
              value={newChapterTitle}
              onChange={(e) => setNewChapterTitle(e.target.value)}
              placeholder="输入章节标题"
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleCreateChapter()
                if (e.key === 'Escape') {
                  setIsCreating(false)
                  setNewChapterTitle('')
                }
              }}
              autoFocus
            />
            <div className="flex justify-end space-x-2 mt-2">
              <button
                onClick={() => {
                  setIsCreating(false)
                  setNewChapterTitle('')
                }}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
              >
                取消
              </button>
              <button
                onClick={handleCreateChapter}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                创建
              </button>
            </div>
          </div>
        )}

        {/* 章节列表 */}
        {chapters.map((chapter) => (
          <ChapterItem
            key={chapter.id}
            chapter={chapter}
            isActive={currentChapter?.id === chapter.id}
            onSelect={(chapter) => switchToChapter(chapter.id)}
            onRename={handleRenameChapter}
            onDelete={handleDeleteChapter}
          />
        ))}

        {chapters.length === 0 && !isCreating && (
          <div className="text-center py-8 text-gray-500">
            <DocumentTextIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <p className="text-sm">还没有章节</p>
            <button
              onClick={() => setIsCreating(true)}
              className="mt-2 text-sm text-blue-600 hover:text-blue-700"
            >
              创建第一个章节
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default ChapterSidebar
