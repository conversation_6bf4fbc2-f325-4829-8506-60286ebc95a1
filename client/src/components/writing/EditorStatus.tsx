import { useMemo } from 'react'
import type { Editor } from '@tiptap/react'
import { 
  ClockIcon, 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  ArrowPathIcon 
} from '@heroicons/react/24/outline'

interface EditorStatusProps {
  editor: Editor
  isDirty: boolean
  isAutoSaving: boolean
  lastSaved: Date | null
  chapterTitle?: string
}

export function EditorStatus({ 
  editor, 
  isDirty, 
  isAutoSaving, 
  lastSaved, 
  chapterTitle 
}: EditorStatusProps) {
  // 计算字数统计
  const stats = useMemo(() => {
    if (!editor) return { characters: 0, words: 0, paragraphs: 0 }
    
    const text = editor.getText()
    const characters = text.length
    const words = text.trim() ? text.trim().split(/\s+/).length : 0
    const paragraphs = editor.state.doc.content.childCount
    
    return { characters, words, paragraphs }
  }, [editor?.state.doc])

  // 格式化最后保存时间
  const formatLastSaved = (date: Date | null) => {
    if (!date) return '未保存'
    
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const minutes = Math.floor(diff / 60000)
    
    if (minutes < 1) return '刚刚保存'
    if (minutes < 60) return `${minutes}分钟前保存`
    
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  // 保存状态指示器
  const SaveStatus = () => {
    if (isAutoSaving) {
      return (
        <div className="flex items-center text-blue-600">
          <ArrowPathIcon className="w-4 h-4 mr-1 animate-spin" />
          <span className="text-sm">保存中...</span>
        </div>
      )
    }
    
    if (isDirty) {
      return (
        <div className="flex items-center text-amber-600">
          <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
          <span className="text-sm">有未保存的更改</span>
        </div>
      )
    }
    
    return (
      <div className="flex items-center text-green-600">
        <CheckCircleIcon className="w-4 h-4 mr-1" />
        <span className="text-sm">{formatLastSaved(lastSaved)}</span>
      </div>
    )
  }

  return (
    <div className="flex items-center justify-between px-4 py-2 bg-gray-50 border border-gray-200 rounded-b-lg border-t-0 text-sm">
      {/* 左侧：章节信息 */}
      <div className="flex items-center space-x-4">
        {chapterTitle && (
          <div className="flex items-center text-gray-600">
            <span className="font-medium">{chapterTitle}</span>
          </div>
        )}
        
        {/* 字数统计 */}
        <div className="flex items-center space-x-3 text-gray-500">
          <span>{stats.words.toLocaleString()} 字</span>
          <span>{stats.characters.toLocaleString()} 字符</span>
          <span>{stats.paragraphs} 段落</span>
        </div>
      </div>

      {/* 右侧：保存状态 */}
      <div className="flex items-center space-x-4">
        {/* 最后保存时间 */}
        <div className="flex items-center text-gray-500">
          <ClockIcon className="w-4 h-4 mr-1" />
          <span>{formatLastSaved(lastSaved)}</span>
        </div>
        
        {/* 保存状态 */}
        <SaveStatus />
      </div>
    </div>
  )
}

export default EditorStatus
