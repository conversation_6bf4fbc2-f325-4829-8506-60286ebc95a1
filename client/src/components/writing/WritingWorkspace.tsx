import React, { useEffect, useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { ArrowLeftIcon, BookOpenIcon, SparklesIcon } from '@heroicons/react/24/outline'
import { novelApi } from '../../services/api'
import { useEditorStore } from '../../stores/editor'
import { ChapterSidebar } from './ChapterSidebar'
import { Editor } from './Editor'
import { AIAssistantPanel } from '../ai/AIAssistantPanel'
import toast from 'react-hot-toast'

// Novel 接口已在 types/novel.ts 中定义

export function WritingWorkspace() {
  const { novelId } = useParams<{ novelId: string }>()
  const navigate = useNavigate()
  const [isAIAssistantOpen, setIsAIAssistantOpen] = useState(false)

  const {
    currentNovel,
    currentChapter,
    setCurrentNovel,
    setCurrentChapter,
    setChapters,
    reset
  } = useEditorStore()

  // 获取小说详情
  const { data: novelResponse, isLoading, error } = useQuery({
    queryKey: ['novel', novelId],
    queryFn: () => novelApi.getNovel(novelId!),
    enabled: !!novelId,
  })

  // 处理小说数据
  React.useEffect(() => {
    if (novelResponse?.data) {
      const novel = novelResponse.data as any
      setCurrentNovel(novel)
      if (novel.chapters && novel.chapters.length > 0) {
        setChapters(novel.chapters)
        // 如果没有当前章节，选择第一个章节
        if (!currentChapter) {
          setCurrentChapter(novel.chapters[0])
        }
      }
    }
  }, [novelResponse, setCurrentNovel, setChapters, setCurrentChapter, currentChapter])

  // 处理错误
  React.useEffect(() => {
    if (error) {
      toast.error('加载小说失败')
      navigate('/dashboard')
    }
  }, [error, navigate])

  // 组件卸载时重置状态
  useEffect(() => {
    return () => {
      reset()
    }
  }, [reset])

  // 处理返回
  const handleBack = () => {
    navigate('/dashboard')
  }

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <BookOpenIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
          <div className="text-gray-500">加载中...</div>
        </div>
      </div>
    )
  }

  if (error || !currentNovel) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 mb-4">加载失败</div>
          <button
            onClick={handleBack}
            className="text-blue-600 hover:text-blue-700"
          >
            返回小说列表
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* 顶部导航栏 */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleBack}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md"
              title="返回小说列表"
            >
              <ArrowLeftIcon className="w-5 h-5" />
            </button>
            
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                {currentNovel.title}
              </h1>
              {currentNovel.description && (
                <p className="text-sm text-gray-600 mt-1">
                  {currentNovel.description}
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* AI 助手按钮 */}
            <button
              onClick={() => setIsAIAssistantOpen(true)}
              className="flex items-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              title="AI 写作助手"
            >
              <SparklesIcon className="w-4 h-4" />
              <span className="hidden sm:inline">AI 助手</span>
            </button>

            {/* 进度信息 */}
            <div className="text-sm text-gray-600">
              <span className="font-medium">
                {currentNovel.wordCount.toLocaleString()}
              </span>
              {currentNovel.targetWordCount && (
                <>
                  <span className="mx-1">/</span>
                  <span>{currentNovel.targetWordCount.toLocaleString()}</span>
                </>
              )}
              <span className="ml-1">字</span>
            </div>

            {/* 状态标签 */}
            <span className={`
              px-2 py-1 rounded-full text-xs font-medium
              ${currentNovel.status === 'writing'
                ? 'bg-blue-100 text-blue-800'
                : currentNovel.status === 'completed'
                ? 'bg-green-100 text-green-800'
                : 'bg-gray-100 text-gray-800'
              }
            `}>
              {currentNovel.status === 'writing' ? '写作中' :
               currentNovel.status === 'completed' ? '已完成' :
               currentNovel.status === 'published' ? '已发布' : '草稿'}
            </span>
          </div>
        </div>
      </header>

      {/* 主要内容区 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 章节侧边栏 */}
        <ChapterSidebar novelId={novelId!} />

        {/* 编辑器区域 */}
        <div className="flex-1 flex flex-col">
          {currentChapter ? (
            <div className="flex-1 p-6">
              <Editor 
                key={currentChapter.id}
                placeholder={`开始写作 "${currentChapter.title}"...`}
              />
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <BookOpenIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  选择一个章节开始写作
                </h3>
                <p className="text-gray-600 mb-6">
                  从左侧选择章节，或创建新的章节
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* AI 助手面板 */}
      <AIAssistantPanel
        novelId={novelId!}
        chapterId={currentChapter?.id}
        isOpen={isAIAssistantOpen}
        onClose={() => setIsAIAssistantOpen(false)}
      />
    </div>
  )
}

export default WritingWorkspace
