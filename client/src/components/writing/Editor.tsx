import { useEffect } from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import { useEditorStore } from '../../stores/editor'
import { EditorToolbar } from './EditorToolbar'
import { EditorStatus } from './EditorStatus'

interface EditorProps {
  className?: string
  placeholder?: string
}

export function Editor({ className = '', placeholder = '开始写作...' }: EditorProps) {
  const { 
    content, 
    setContent, 
    isDirty, 
    isAutoSaving, 
    lastSaved,
    currentChapter 
  } = useEditorStore()

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3, 4, 5, 6],
        },
        bulletList: {
          keepMarks: true,
          keepAttributes: false,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false,
        },
      }),
      Placeholder.configure({
        placeholder,
        emptyEditorClass: 'is-editor-empty',
      }),
    ],
    content,
    editorProps: {
      attributes: {
        class: 'prose prose-lg max-w-none focus:outline-none min-h-[500px] p-6',
      },
    },
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      setContent(html)
    },
    immediatelyRender: false,
  })

  // 当章节切换时更新编辑器内容
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content)
    }
  }, [editor, content])

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + S 保存
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault()
        useEditorStore.getState().saveContent()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  if (!editor) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">编辑器加载中...</div>
      </div>
    )
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* 工具栏 */}
      <EditorToolbar editor={editor} />
      
      {/* 编辑器内容区 */}
      <div className="flex-1 overflow-auto bg-white border border-gray-200 rounded-lg">
        <EditorContent 
          editor={editor} 
          className="h-full"
        />
      </div>
      
      {/* 状态栏 */}
      <EditorStatus 
        editor={editor}
        isDirty={isDirty}
        isAutoSaving={isAutoSaving}
        lastSaved={lastSaved}
        chapterTitle={currentChapter?.title}
      />
    </div>
  )
}

export default Editor
