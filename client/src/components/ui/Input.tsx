import React from 'react'
import { clsx } from 'clsx'

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /** 输入框标签 */
  label?: string
  /** 帮助文本 */
  helperText?: string
  /** 错误信息 */
  error?: string
  /** 输入框尺寸 */
  size?: 'sm' | 'md' | 'lg'
  /** 左侧图标 */
  leftIcon?: React.ReactNode
  /** 右侧图标 */
  rightIcon?: React.ReactNode
  /** 是否全宽 */
  fullWidth?: boolean
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      helperText,
      error,
      size = 'md',
      leftIcon,
      rightIcon,
      fullWidth = true,
      className,
      id,
      required,
      disabled,
      ...props
    },
    ref
  ) => {
    // 生成唯一ID
    const inputId = id || React.useId()
    const helperTextId = `${inputId}-helper`
    const errorId = `${inputId}-error`

    // 基础样式
    const baseClasses = 'input'

    // 尺寸样式
    const sizeClasses = {
      sm: 'text-sm px-3 py-1.5',
      md: 'text-sm px-3 py-2',
      lg: 'text-base px-4 py-3',
    }

    // 图标内边距调整
    const iconPaddingClasses = {
      left: leftIcon ? 'pl-10' : '',
      right: rightIcon ? 'pr-10' : '',
    }

    // 组合输入框样式
    const inputClasses = clsx(
      baseClasses,
      sizeClasses[size],
      iconPaddingClasses.left,
      iconPaddingClasses.right,
      {
        'input-error': error,
        'w-full': fullWidth,
        'disabled:opacity-50 disabled:cursor-not-allowed': disabled,
      },
      className
    )

    // 容器样式
    const containerClasses = clsx({
      'w-full': fullWidth,
    })

    // 图标容器样式
    const iconContainerClasses = 'absolute inset-y-0 flex items-center pointer-events-none'
    const leftIconClasses = clsx(iconContainerClasses, 'left-0 pl-3')
    const rightIconClasses = clsx(iconContainerClasses, 'right-0 pr-3')

    return (
      <div className={containerClasses}>
        {/* 标签 */}
        {label && (
          <label
            htmlFor={inputId}
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            {label}
            {required && (
              <span className="text-red-500 ml-1" aria-label="必填">
                *
              </span>
            )}
          </label>
        )}

        {/* 输入框容器 */}
        <div className="relative">
          {/* 左侧图标 */}
          {leftIcon && (
            <div className={leftIconClasses}>
              <span className="text-gray-400">
                {leftIcon}
              </span>
            </div>
          )}

          {/* 输入框 */}
          <input
            ref={ref}
            id={inputId}
            className={inputClasses}
            disabled={disabled}
            required={required}
            aria-required={required}
            aria-invalid={!!error}
            aria-describedby={
              error ? errorId : helperText ? helperTextId : undefined
            }
            {...props}
          />

          {/* 右侧图标 */}
          {rightIcon && (
            <div className={rightIconClasses}>
              <span className="text-gray-400">
                {rightIcon}
              </span>
            </div>
          )}
        </div>

        {/* 帮助文本或错误信息 */}
        {(error || helperText) && (
          <div className="mt-1">
            {error ? (
              <p
                id={errorId}
                className="text-sm text-red-600"
                role="alert"
              >
                {error}
              </p>
            ) : (
              helperText && (
                <p
                  id={helperTextId}
                  className="text-sm text-gray-500"
                >
                  {helperText}
                </p>
              )
            )}
          </div>
        )}
      </div>
    )
  }
)

Input.displayName = 'Input'
