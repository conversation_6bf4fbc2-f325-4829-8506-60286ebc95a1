import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { Input } from './Input'

describe('Input组件', () => {
  describe('基础功能', () => {
    it('应该渲染输入框', () => {
      render(<Input placeholder="请输入内容" />)
      expect(screen.getByPlaceholderText('请输入内容')).toBeInTheDocument()
    })

    it('应该处理值变化', () => {
      const handleChange = vi.fn()
      render(<Input onChange={handleChange} />)
      
      const input = screen.getByRole('textbox')
      fireEvent.change(input, { target: { value: '测试内容' } })
      
      expect(handleChange).toHaveBeenCalledTimes(1)
      expect(handleChange).toHaveBeenCalledWith(
        expect.objectContaining({
          target: expect.objectContaining({
            value: '测试内容',
          }),
        })
      )
    })

    it('应该显示默认值', () => {
      render(<Input defaultValue="默认值" />)
      const input = screen.getByRole('textbox') as HTMLInputElement
      expect(input.value).toBe('默认值')
    })

    it('应该支持受控组件', () => {
      const { rerender } = render(<Input value="初始值" onChange={() => {}} />)
      const input = screen.getByRole('textbox') as HTMLInputElement
      expect(input.value).toBe('初始值')

      rerender(<Input value="更新值" onChange={() => {}} />)
      expect(input.value).toBe('更新值')
    })
  })

  describe('标签和描述', () => {
    it('应该显示标签', () => {
      render(<Input label="用户名" />)
      expect(screen.getByText('用户名')).toBeInTheDocument()
    })

    it('应该显示必填标记', () => {
      render(<Input label="邮箱" required />)
      expect(screen.getByText('*')).toBeInTheDocument()
    })

    it('应该显示帮助文本', () => {
      render(<Input helperText="请输入有效的邮箱地址" />)
      expect(screen.getByText('请输入有效的邮箱地址')).toBeInTheDocument()
    })

    it('应该关联标签和输入框', () => {
      render(<Input label="密码" />)
      const label = screen.getByText('密码')
      const input = screen.getByRole('textbox')
      
      expect(label).toHaveAttribute('for', input.id)
      expect(input).toHaveAttribute('id')
    })
  })

  describe('错误状态', () => {
    it('应该显示错误信息', () => {
      render(<Input error="邮箱格式不正确" />)
      expect(screen.getByText('邮箱格式不正确')).toBeInTheDocument()
    })

    it('应该应用错误样式', () => {
      render(<Input error="错误信息" />)
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('input-error')
    })

    it('应该优先显示错误信息而不是帮助文本', () => {
      render(
        <Input 
          helperText="帮助文本" 
          error="错误信息" 
        />
      )
      
      expect(screen.getByText('错误信息')).toBeInTheDocument()
      expect(screen.queryByText('帮助文本')).not.toBeInTheDocument()
    })

    it('应该设置正确的aria属性', () => {
      render(<Input error="错误信息" />)
      const input = screen.getByRole('textbox')
      
      expect(input).toHaveAttribute('aria-invalid', 'true')
      expect(input).toHaveAttribute('aria-describedby')
    })
  })

  describe('禁用状态', () => {
    it('应该禁用输入框', () => {
      render(<Input disabled />)
      const input = screen.getByRole('textbox')
      expect(input).toBeDisabled()
    })

    it('应该应用禁用样式', () => {
      render(<Input disabled />)
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('disabled:opacity-50', 'disabled:cursor-not-allowed')
    })
  })

  describe('输入类型', () => {
    it('应该支持不同的输入类型', () => {
      const { rerender } = render(<Input type="email" />)
      let input = screen.getByRole('textbox')
      expect(input).toHaveAttribute('type', 'email')

      rerender(<Input type="password" />)
      input = screen.getByLabelText('', { selector: 'input[type="password"]' })
      expect(input).toHaveAttribute('type', 'password')

      rerender(<Input type="number" />)
      input = screen.getByRole('spinbutton')
      expect(input).toHaveAttribute('type', 'number')
    })
  })

  describe('图标支持', () => {
    it('应该显示左侧图标', () => {
      const LeftIcon = () => <span data-testid="left-icon">📧</span>
      render(<Input leftIcon={<LeftIcon />} />)
      
      expect(screen.getByTestId('left-icon')).toBeInTheDocument()
    })

    it('应该显示右侧图标', () => {
      const RightIcon = () => <span data-testid="right-icon">👁️</span>
      render(<Input rightIcon={<RightIcon />} />)
      
      expect(screen.getByTestId('right-icon')).toBeInTheDocument()
    })

    it('应该调整有图标时的内边距', () => {
      const LeftIcon = () => <span data-testid="left-icon">📧</span>
      render(<Input leftIcon={<LeftIcon />} />)
      
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('pl-10')
    })
  })

  describe('尺寸变体', () => {
    it('应该应用small尺寸', () => {
      render(<Input size="sm" />)
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('text-sm', 'px-3', 'py-1.5')
    })

    it('应该应用medium尺寸（默认）', () => {
      render(<Input />)
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('text-sm', 'px-3', 'py-2')
    })

    it('应该应用large尺寸', () => {
      render(<Input size="lg" />)
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('text-base', 'px-4', 'py-3')
    })
  })

  describe('自定义属性', () => {
    it('应该传递自定义className', () => {
      render(<Input className="custom-input" />)
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('custom-input')
    })

    it('应该传递其他HTML属性', () => {
      render(
        <Input 
          maxLength={10}
          autoComplete="email"
          data-testid="email-input"
        />
      )
      const input = screen.getByRole('textbox')
      
      expect(input).toHaveAttribute('maxLength', '10')
      expect(input).toHaveAttribute('autoComplete', 'email')
      expect(input).toHaveAttribute('data-testid', 'email-input')
    })
  })

  describe('可访问性', () => {
    it('应该有正确的role属性', () => {
      render(<Input />)
      expect(screen.getByRole('textbox')).toBeInTheDocument()
    })

    it('应该支持aria-label', () => {
      render(<Input aria-label="搜索内容" />)
      const input = screen.getByRole('textbox')
      expect(input).toHaveAttribute('aria-label', '搜索内容')
    })

    it('应该在必填时设置aria-required', () => {
      render(<Input required />)
      const input = screen.getByRole('textbox')
      expect(input).toHaveAttribute('aria-required', 'true')
    })
  })
})
