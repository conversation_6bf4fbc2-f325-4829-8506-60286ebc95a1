import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from './Button'

describe('Button组件', () => {
  describe('基础功能', () => {
    it('应该渲染按钮文本', () => {
      render(<Button>点击我</Button>)
      expect(screen.getByRole('button')).toHaveTextContent('点击我')
    })

    it('应该处理点击事件', () => {
      const handleClick = vi.fn()
      render(<Button onClick={handleClick}>点击我</Button>)
      
      fireEvent.click(screen.getByRole('button'))
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('应该在禁用时不触发点击事件', () => {
      const handleClick = vi.fn()
      render(
        <Button onClick={handleClick} disabled>
          禁用按钮
        </Button>
      )
      
      fireEvent.click(screen.getByRole('button'))
      expect(handleClick).not.toHaveBeenCalled()
    })
  })

  describe('样式变体', () => {
    it('应该应用primary样式', () => {
      render(<Button variant="primary">主要按钮</Button>)
      const button = screen.getByRole('button')
      expect(button).toHaveClass('btn-primary')
    })

    it('应该应用secondary样式', () => {
      render(<Button variant="secondary">次要按钮</Button>)
      const button = screen.getByRole('button')
      expect(button).toHaveClass('btn-secondary')
    })

    it('应该应用outline样式', () => {
      render(<Button variant="outline">轮廓按钮</Button>)
      const button = screen.getByRole('button')
      expect(button).toHaveClass('btn-outline')
    })

    it('应该应用ghost样式', () => {
      render(<Button variant="ghost">幽灵按钮</Button>)
      const button = screen.getByRole('button')
      expect(button).toHaveClass('btn-ghost')
    })

    it('应该应用danger样式', () => {
      render(<Button variant="danger">危险按钮</Button>)
      const button = screen.getByRole('button')
      expect(button).toHaveClass('btn-danger')
    })
  })

  describe('尺寸变体', () => {
    it('应该应用small尺寸', () => {
      render(<Button size="sm">小按钮</Button>)
      const button = screen.getByRole('button')
      expect(button).toHaveClass('text-xs', 'px-3', 'py-1.5')
    })

    it('应该应用medium尺寸（默认）', () => {
      render(<Button>中等按钮</Button>)
      const button = screen.getByRole('button')
      expect(button).toHaveClass('text-sm', 'px-4', 'py-2')
    })

    it('应该应用large尺寸', () => {
      render(<Button size="lg">大按钮</Button>)
      const button = screen.getByRole('button')
      expect(button).toHaveClass('text-base', 'px-6', 'py-3')
    })
  })

  describe('加载状态', () => {
    it('应该显示加载状态', () => {
      render(<Button loading>加载中</Button>)
      const button = screen.getByRole('button')
      
      expect(button).toBeDisabled()
      expect(button).toHaveClass('opacity-50')
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    })

    it('应该在加载时隐藏按钮文本', () => {
      render(<Button loading>按钮文本</Button>)
      const button = screen.getByRole('button')
      
      // 文本应该被隐藏
      expect(button.querySelector('.opacity-0')).toBeInTheDocument()
    })

    it('应该在加载时不响应点击', () => {
      const handleClick = vi.fn()
      render(
        <Button loading onClick={handleClick}>
          加载按钮
        </Button>
      )
      
      fireEvent.click(screen.getByRole('button'))
      expect(handleClick).not.toHaveBeenCalled()
    })
  })

  describe('图标支持', () => {
    it('应该显示左侧图标', () => {
      const LeftIcon = () => <span data-testid="left-icon">←</span>
      render(
        <Button leftIcon={<LeftIcon />}>
          带图标按钮
        </Button>
      )
      
      expect(screen.getByTestId('left-icon')).toBeInTheDocument()
    })

    it('应该显示右侧图标', () => {
      const RightIcon = () => <span data-testid="right-icon">→</span>
      render(
        <Button rightIcon={<RightIcon />}>
          带图标按钮
        </Button>
      )
      
      expect(screen.getByTestId('right-icon')).toBeInTheDocument()
    })

    it('应该同时显示左右图标', () => {
      const LeftIcon = () => <span data-testid="left-icon">←</span>
      const RightIcon = () => <span data-testid="right-icon">→</span>
      
      render(
        <Button leftIcon={<LeftIcon />} rightIcon={<RightIcon />}>
          双图标按钮
        </Button>
      )
      
      expect(screen.getByTestId('left-icon')).toBeInTheDocument()
      expect(screen.getByTestId('right-icon')).toBeInTheDocument()
    })
  })

  describe('全宽按钮', () => {
    it('应该应用全宽样式', () => {
      render(<Button fullWidth>全宽按钮</Button>)
      const button = screen.getByRole('button')
      expect(button).toHaveClass('w-full')
    })
  })

  describe('自定义属性', () => {
    it('应该传递自定义className', () => {
      render(<Button className="custom-class">自定义按钮</Button>)
      const button = screen.getByRole('button')
      expect(button).toHaveClass('custom-class')
    })

    it('应该传递其他HTML属性', () => {
      render(
        <Button type="submit" data-testid="submit-button">
          提交按钮
        </Button>
      )
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('type', 'submit')
      expect(button).toHaveAttribute('data-testid', 'submit-button')
    })
  })

  describe('可访问性', () => {
    it('应该有正确的role属性', () => {
      render(<Button>按钮</Button>)
      expect(screen.getByRole('button')).toBeInTheDocument()
    })

    it('应该支持aria-label', () => {
      render(<Button aria-label="关闭对话框">×</Button>)
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('aria-label', '关闭对话框')
    })

    it('应该在禁用时有正确的aria-disabled', () => {
      render(<Button disabled>禁用按钮</Button>)
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('aria-disabled', 'true')
    })
  })
})
