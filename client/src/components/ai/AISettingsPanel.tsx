import { useState } from 'react'
import { Cog6ToothIcon, ServerIcon } from '@heroicons/react/24/outline'
import { AIProviderPanel } from './AIProviderPanel'
import { AIModelSelector } from './AIModelSelector'
import { useAIConfigStore } from '../../stores/aiConfig'

export function AISettingsPanel() {
  const [activeTab, setActiveTab] = useState<'general' | 'providers'>('general')
  const {
    temperature,
    maxTokens,
    autoSave,
    realTimeSuggestions,
    setTemperature,
    setMaxTokens,
    setAutoSave,
    setRealTimeSuggestions
  } = useAIConfigStore()

  return (
    <div className="p-6 space-y-6">
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Cog6ToothIcon className="w-5 h-5 text-gray-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-gray-900">AI 设置</h3>
            <p className="text-sm text-gray-700 mt-1">
              配置 AI 模型参数和服务提供商。
            </p>
          </div>
        </div>
      </div>

      {/* 标签页 */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('general')}
            className={`
              py-2 px-1 border-b-2 font-medium text-sm
              ${activeTab === 'general'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }
            `}
          >
            <div className="flex items-center space-x-2">
              <Cog6ToothIcon className="w-4 h-4" />
              <span>通用设置</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('providers')}
            className={`
              py-2 px-1 border-b-2 font-medium text-sm
              ${activeTab === 'providers'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }
            `}
          >
            <div className="flex items-center space-x-2">
              <ServerIcon className="w-4 h-4" />
              <span>Provider 管理</span>
            </div>
          </button>
        </nav>
      </div>

      {/* 内容区域 */}
      {activeTab === 'general' ? (
        <div className="space-y-6">
        {/* AI 模型选择 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            当前使用的 AI 模型
          </label>
          <AIModelSelector />
          <p className="text-xs text-gray-500 mt-1">
            在"Provider 管理"中可以添加更多 AI 服务提供商
          </p>
        </div>

        {/* 创造性设置 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            创造性 (Temperature): {temperature}
          </label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={temperature}
            onChange={(e) => setTemperature(parseFloat(e.target.value))}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>保守</span>
            <span>创新</span>
          </div>
        </div>

        {/* 最大输出长度 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            最大输出长度
          </label>
          <select
            value={maxTokens}
            onChange={(e) => setMaxTokens(parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="500">短 (500 tokens)</option>
            <option value="1000">中 (1000 tokens)</option>
            <option value="2000">长 (2000 tokens)</option>
            <option value="4000">超长 (4000 tokens)</option>
          </select>
        </div>

        {/* 功能开关 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium text-gray-900">自动保存 AI 生成内容</div>
              <div className="text-sm text-gray-500">自动保存接受的 AI 建议</div>
            </div>
            <button
              type="button"
              onClick={() => setAutoSave(!autoSave)}
              className={`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                ${autoSave ? 'bg-blue-600' : 'bg-gray-200'}
              `}
            >
              <span
                className={`
                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                  ${autoSave ? 'translate-x-6' : 'translate-x-1'}
                `}
              />
            </button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium text-gray-900">实时写作建议</div>
              <div className="text-sm text-gray-500">在写作时显示实时建议</div>
            </div>
            <button
              type="button"
              onClick={() => setRealTimeSuggestions(!realTimeSuggestions)}
              className={`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                ${realTimeSuggestions ? 'bg-blue-600' : 'bg-gray-200'}
              `}
            >
              <span
                className={`
                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                  ${realTimeSuggestions ? 'translate-x-6' : 'translate-x-1'}
                `}
              />
            </button>
          </div>
        </div>

        {/* 保存按钮 */}
        <div className="pt-6 border-t border-gray-200">
          <button className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            保存设置
          </button>
        </div>
        </div>
      ) : (
        <AIProviderPanel embedded={true} />
      )}
    </div>
  )
}

export default AISettingsPanel
