import { useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { DocumentTextIcon, ArrowPathIcon } from '@heroicons/react/24/outline'
import { useEditorStore } from '../../stores/editor'
import { AIService } from '../../services/aiService'
import toast from 'react-hot-toast'

interface AISummaryPanelProps {
  novelId: string
}

export function AISummaryPanel({ novelId: _novelId }: AISummaryPanelProps) {
  const [summaryType, setSummaryType] = useState<'chapter' | 'plot_points' | 'characters' | 'world_building'>('chapter')
  const [summaryResult, setSummaryResult] = useState<any>(null)

  const { content } = useEditorStore()

  const summaryMutation = useMutation({
    mutationFn: async () => {
      return AIService.summarizeContent({
        content,
        type: summaryType,
        maxLength: 500
      })
    },
    onSuccess: (data) => {
      setSummaryResult(data)
      toast.success('总结完成')
    },
    onError: (error) => {
      console.error('AI 总结失败:', error)
      toast.error('总结失败，请重试')
    }
  })

  return (
    <div className="p-6 space-y-6">
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <DocumentTextIcon className="w-5 h-5 text-green-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-green-900">智能总结</h3>
            <p className="text-sm text-green-700 mt-1">
              AI 将为你的内容生成智能总结，提取关键情节点和角色信息。
            </p>
          </div>
        </div>
      </div>

      {/* 总结类型选择 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          总结类型
        </label>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {[
            { value: 'chapter', label: '章节总结' },
            { value: 'plot_points', label: '情节要点' },
            { value: 'characters', label: '角色分析' },
            { value: 'world_building', label: '世界设定' }
          ].map((type) => (
            <button
              key={type.value}
              onClick={() => setSummaryType(type.value as any)}
              className={`
                p-3 text-center border rounded-lg transition-colors
                ${summaryType === type.value
                  ? 'border-green-300 bg-green-50 text-green-700'
                  : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }
              `}
            >
              {type.label}
            </button>
          ))}
        </div>
      </div>

      {/* 生成按钮 */}
      <div className="flex justify-center">
        <button
          onClick={() => summaryMutation.mutate()}
          disabled={summaryMutation.isPending || !content.trim()}
          className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center space-x-2"
        >
          {summaryMutation.isPending ? (
            <ArrowPathIcon className="w-5 h-5 animate-spin" />
          ) : (
            <DocumentTextIcon className="w-5 h-5" />
          )}
          <span>
            {summaryMutation.isPending ? '生成中...' : '生成总结'}
          </span>
        </button>
      </div>

      {/* 总结结果 */}
      {summaryResult && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">总结结果</h3>
          <div className="prose prose-sm max-w-none">
            <p className="text-gray-700">{summaryResult.summary}</p>
          </div>
          
          {summaryResult.keyPoints && summaryResult.keyPoints.length > 0 && (
            <div className="mt-6">
              <h4 className="font-medium text-gray-900 mb-3">关键要点</h4>
              <ul className="space-y-2">
                {summaryResult.keyPoints.map((point: string, index: number) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                    <span className="text-gray-700">{point}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {!summaryResult && !summaryMutation.isPending && (
        <div className="text-center py-8 text-gray-500">
          <DocumentTextIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p>选择总结类型并点击"生成总结"</p>
        </div>
      )}
    </div>
  )
}

export default AISummaryPanel
