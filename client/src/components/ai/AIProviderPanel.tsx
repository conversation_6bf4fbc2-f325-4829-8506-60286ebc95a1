import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  StarIcon,
  WifiIcon
} from '@heroicons/react/24/outline'
import { AIProviderService, AIProvider } from '../../services/aiProviderService'
import { AIProviderForm } from './AIProviderForm'
import toast from 'react-hot-toast'

interface AIProviderPanelProps {
  embedded?: boolean // 是否嵌入在其他组件中
}

export function AIProviderPanel({ embedded = false }: AIProviderPanelProps) {
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingProvider, setEditingProvider] = useState<AIProvider | null>(null)
  const queryClient = useQueryClient()

  // 获取 AI Providers
  const { data: providersData = [], isLoading } = useQuery({
    queryKey: ['ai-providers'],
    queryFn: AIProviderService.getProviders
  })

  // 确保 providers 是数组
  const providers = Array.isArray(providersData) ? providersData : []

  // 删除 Provider
  const deleteMutation = useMutation({
    mutationFn: AIProviderService.deleteProvider,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ai-providers'] })
      toast.success('AI Provider 删除成功')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error?.message || '删除失败')
    }
  })

  // 设置默认 Provider
  const setDefaultMutation = useMutation({
    mutationFn: AIProviderService.setDefaultProvider,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ai-providers'] })
      toast.success('默认 AI Provider 设置成功')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error?.message || '设置失败')
    }
  })

  // 测试连接
  const testMutation = useMutation({
    mutationFn: AIProviderService.testProvider,
    onSuccess: (data) => {
      if (data.success) {
        toast.success(`连接测试成功 (延迟: ${data.latency}ms)`)
      } else {
        toast.error('连接测试失败')
      }
    },
    onError: () => {
      toast.error('连接测试失败')
    }
  })

  const handleEdit = (provider: AIProvider) => {
    setEditingProvider(provider)
    setIsFormOpen(true)
  }

  const handleDelete = (provider: AIProvider) => {
    if (provider.isDefault) {
      toast.error('不能删除默认 AI Provider')
      return
    }
    
    if (confirm(`确定要删除 "${provider.name}" 吗？`)) {
      deleteMutation.mutate(provider.id)
    }
  }

  const handleSetDefault = (provider: AIProvider) => {
    setDefaultMutation.mutate(provider.id)
  }

  const handleTest = (provider: AIProvider) => {
    testMutation.mutate(provider.id)
  }

  const handleFormClose = () => {
    setIsFormOpen(false)
    setEditingProvider(null)
  }

  const getStatusColor = (provider: AIProvider) => {
    if (!provider.isActive) return 'text-gray-400'
    return provider.isDefault ? 'text-green-600' : 'text-blue-600'
  }

  const getStatusIcon = (provider: AIProvider) => {
    if (!provider.isActive) return XCircleIcon
    return provider.isDefault ? StarIcon : CheckCircleIcon
  }

  if (isLoading) {
    return (
      <div className={embedded ? "p-4" : "p-6"}>
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${embedded ? "p-4" : "p-6"}`}>
      {/* 头部 */}
      {!embedded && (
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">AI Provider 管理</h2>
            <p className="text-sm text-gray-600 mt-1">
              管理和配置 AI 服务提供商
            </p>
          </div>
          <button
            type="button"
            onClick={() => setIsFormOpen(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <PlusIcon className="w-4 h-4" />
            <span>添加 Provider</span>
          </button>
        </div>
      )}

      {/* 嵌入模式的简化头部 */}
      {embedded && (
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">AI Provider 配置</h3>
          <button
            type="button"
            onClick={() => setIsFormOpen(true)}
            className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
          >
            <PlusIcon className="w-4 h-4" />
            <span>添加</span>
          </button>
        </div>
      )}

      {/* Provider 列表 */}
      <div className={embedded ? "space-y-3" : "space-y-4"}>
        {providers.map((provider) => {
          const StatusIcon = getStatusIcon(provider)

          // 如果正在编辑这个 provider，显示编辑表单
          if (isFormOpen && editingProvider?.id === provider.id) {
            return (
              <div key={provider.id} className="border border-blue-300 rounded-lg">
                <AIProviderForm
                  provider={editingProvider}
                  onClose={handleFormClose}
                  onSuccess={() => {
                    queryClient.invalidateQueries({ queryKey: ['ai-providers'] })
                    handleFormClose()
                  }}
                  embedded={true}
                />
              </div>
            )
          }

          return (
            <div
              key={provider.id}
              className={`
                border rounded-lg transition-colors
                ${embedded ? 'p-3' : 'p-4'}
                ${provider.isDefault
                  ? 'border-green-300 bg-green-50'
                  : provider.isActive
                  ? 'border-gray-200 bg-white'
                  : 'border-gray-200 bg-gray-50'
                }
              `}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <StatusIcon className={`w-5 h-5 ${getStatusColor(provider)}`} />
                  <div>
                    <div className="flex items-center space-x-2">
                      <h3 className="font-medium text-gray-900">{provider.name}</h3>
                      {provider.isDefault && (
                        <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                          默认
                        </span>
                      )}
                      {!provider.isActive && (
                        <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                          未激活
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      <span className="font-medium">{provider.type}</span>
                      <span className="mx-2">•</span>
                      <span>{provider.baseUrl}</span>
                      <span className="mx-2">•</span>
                      <span>{provider.models.length} 个模型</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {/* 测试连接按钮 */}
                  <button
                    type="button"
                    onClick={() => handleTest(provider)}
                    disabled={testMutation.isPending}
                    className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md"
                    title="测试连接"
                  >
                    <WifiIcon className="w-4 h-4" />
                  </button>

                  {/* 设为默认按钮 */}
                  {!provider.isDefault && provider.isActive && (
                    <button
                      type="button"
                      onClick={() => handleSetDefault(provider)}
                      disabled={setDefaultMutation.isPending}
                      className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-md"
                      title="设为默认"
                    >
                      <StarIcon className="w-4 h-4" />
                    </button>
                  )}

                  {/* 编辑按钮 */}
                  <button
                    type="button"
                    onClick={() => handleEdit(provider)}
                    className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md"
                    title="编辑"
                  >
                    <PencilIcon className="w-4 h-4" />
                  </button>

                  {/* 删除按钮 */}
                  {!provider.isDefault && (
                    <button
                      type="button"
                      onClick={() => handleDelete(provider)}
                      disabled={deleteMutation.isPending}
                      className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md"
                      title="删除"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>

              {/* 模型列表 */}
              <div className="mt-3 flex flex-wrap gap-2">
                {provider.models.slice(0, 5).map((model) => (
                  <span
                    key={model.id}
                    className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                  >
                    {model.name}
                  </span>
                ))}
                {provider.models.length > 5 && (
                  <span className="px-2 py-1 text-xs bg-gray-100 text-gray-500 rounded">
                    +{provider.models.length - 5} 更多
                  </span>
                )}
              </div>
            </div>
          )
        })}

        {/* 新建 Provider 表单 */}
        {isFormOpen && !editingProvider && (
          <div className="border border-blue-300 rounded-lg">
            <AIProviderForm
              provider={null}
              onClose={handleFormClose}
              onSuccess={() => {
                queryClient.invalidateQueries({ queryKey: ['ai-providers'] })
                handleFormClose()
              }}
              embedded={true}
            />
          </div>
        )}

        {providers.length === 0 && !isFormOpen && (
          <div className="text-center py-8 text-gray-500">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <PlusIcon className="w-8 h-8 text-gray-400" />
            </div>
            <p>还没有配置 AI Provider</p>
            <p className="text-sm mt-1">点击"添加 Provider"开始配置</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default AIProviderPanel
