import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { ChevronDownIcon, CheckIcon } from '@heroicons/react/24/outline'
import { AIProviderService, AIProvider } from '../../services/aiProviderService'
import { useAIConfigStore } from '../../stores/aiConfig'

export function AIModelSelector() {
  const [isOpen, setIsOpen] = useState(false)
  const {
    selectedProviderId,
    selectedModelId,
    setSelectedProvider,
    setSelectedModel
  } = useAIConfigStore()

  // 获取激活的 Providers
  const { data: providers = [] } = useQuery({
    queryKey: ['ai-providers', 'active'],
    queryFn: AIProviderService.getActiveProviders
  })

  // 找到当前选择的 Provider 和 Model
  const currentProvider = providers.find(p => p.id === selectedProviderId) || 
                         providers.find(p => p.isDefault) || 
                         providers[0]
  
  const currentModel = currentProvider?.models.find(m => m.id === selectedModelId) || 
                      currentProvider?.models[0]

  // 当 Provider 列表加载完成时，设置默认选择
  useEffect(() => {
    if (providers.length > 0 && !selectedProviderId) {
      const defaultProvider = providers.find(p => p.isDefault) || providers[0]
      if (defaultProvider) {
        setSelectedProvider(defaultProvider.id)
        if (defaultProvider.models.length > 0) {
          setSelectedModel(defaultProvider.models[0].id)
        }
      }
    }
  }, [providers, selectedProviderId, setSelectedProvider, setSelectedModel])

  const handleSelectModel = (provider: AIProvider, modelId: string) => {
    setSelectedProvider(provider.id)
    setSelectedModel(modelId)
    setIsOpen(false)
  }

  if (!currentProvider || !currentModel) {
    return (
      <div className="text-sm text-gray-500">
        没有可用的 AI 模型
      </div>
    )
  }

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <div className="flex items-center space-x-2">
          <span className="font-medium">{currentProvider.name}</span>
          <span className="text-gray-500">•</span>
          <span>{currentModel.name}</span>
        </div>
        <ChevronDownIcon className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {providers.map((provider) => (
            <div key={provider.id}>
              {/* Provider 标题 */}
              <div className="px-3 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b">
                {provider.name}
                {provider.isDefault && (
                  <span className="ml-2 px-1.5 py-0.5 text-xs bg-green-100 text-green-800 rounded">
                    默认
                  </span>
                )}
              </div>
              
              {/* Provider 的模型列表 */}
              {provider.models.map((model) => (
                <button
                  key={`${provider.id}-${model.id}`}
                  onClick={() => handleSelectModel(provider, model.id)}
                  className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center justify-between"
                >
                  <div>
                    <div className="font-medium text-gray-900">{model.name}</div>
                    <div className="text-xs text-gray-500">
                      {model.type === 'text' && '文本生成'}
                      {model.type === 'image' && '图像生成'}
                      {model.type === 'multimodal' && '多模态'}
                    </div>
                  </div>
                  
                  {currentProvider.id === provider.id && currentModel.id === model.id && (
                    <CheckIcon className="w-4 h-4 text-blue-600" />
                  )}
                </button>
              ))}
            </div>
          ))}
          
          {providers.length === 0 && (
            <div className="px-3 py-4 text-sm text-gray-500 text-center">
              没有可用的 AI Provider
              <br />
              <span className="text-xs">请在 Provider 管理中配置</span>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default AIModelSelector
