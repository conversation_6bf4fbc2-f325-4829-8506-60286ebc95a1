import { LightBulbIcon } from '@heroicons/react/24/outline'

interface AIOutlinePanelProps {
  novelId: string
}

export function AIOutlinePanel({ novelId: _novelId }: AIOutlinePanelProps) {
  return (
    <div className="p-6 space-y-6">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <LightBulbIcon className="w-5 h-5 text-yellow-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-yellow-900">AI 大纲助手</h3>
            <p className="text-sm text-yellow-700 mt-1">
              基于小说设定和已有内容，AI 将为你生成详细的故事大纲。
            </p>
          </div>
        </div>
      </div>

      <div className="text-center py-8 text-gray-500">
        <LightBulbIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
        <p>大纲生成功能开发中...</p>
      </div>
    </div>
  )
}

export default AIOutlinePanel
