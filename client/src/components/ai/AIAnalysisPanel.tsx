import { useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { 
  ChartBarIcon, 
  ArrowPathIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { useEditorStore } from '../../stores/editor'
import { AIService, AIAnalysisResponse } from '../../services/aiService'
import toast from 'react-hot-toast'

interface AIAnalysisPanelProps {
  novelId: string
  chapterId?: string
}

export function AIAnalysisPanel({ novelId, chapterId }: AIAnalysisPanelProps) {
  const [analysisType, setAnalysisType] = useState<'style' | 'character' | 'plot' | 'pacing' | 'consistency'>('style')
  const [analysisResult, setAnalysisResult] = useState<AIAnalysisResponse | null>(null)

  const { content, currentChapter, currentNovel } = useEditorStore()

  // AI 分析 mutation
  const analysisMutation = useMutation({
    mutationFn: async () => {
      if (!content.trim()) {
        throw new Error('请先输入一些内容')
      }

      const context = currentNovel && currentChapter ? {
        novelId,
        chapterId: chapterId || currentChapter.id,
        previousContent: content,
        characters: [], // TODO: 从数据库获取
        worldSettings: [], // TODO: 从数据库获取
        outline: null, // TODO: 从数据库获取
        styleGuide: (currentNovel as any).styleSample || undefined
      } : undefined

      return AIService.analyzeContent({
        content,
        type: analysisType,
        context
      })
    },
    onSuccess: (data) => {
      setAnalysisResult(data)
      toast.success('分析完成')
    },
    onError: (error) => {
      console.error('AI 分析失败:', error)
      toast.error('分析失败，请重试')
    }
  })

  const handleAnalyze = () => {
    if (!content.trim()) {
      toast.error('请先在编辑器中输入一些内容')
      return
    }
    analysisMutation.mutate()
  }

  const analysisTypes = [
    { value: 'style', label: '写作风格', description: '分析文本的语言风格、语调和表达方式' },
    { value: 'character', label: '角色分析', description: '检查角色一致性和发展' },
    { value: 'plot', label: '情节分析', description: '评估情节结构和逻辑性' },
    { value: 'pacing', label: '节奏分析', description: '分析故事节奏和张力' },
    { value: 'consistency', label: '一致性检查', description: '检查前后文的一致性' }
  ]

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200'
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'low': return 'text-blue-600 bg-blue-50 border-blue-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high': return ExclamationTriangleIcon
      case 'medium': return InformationCircleIcon
      case 'low': return CheckCircleIcon
      default: return InformationCircleIcon
    }
  }

  return (
    <div className="p-6 space-y-6">
      {/* 头部说明 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <ChartBarIcon className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-blue-900">AI 内容分析</h3>
            <p className="text-sm text-blue-700 mt-1">
              AI 将分析你的文本内容，提供专业的写作建议和改进意见。
            </p>
          </div>
        </div>
      </div>

      {/* 分析类型选择 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          选择分析类型
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {analysisTypes.map((type) => (
            <button
              key={type.value}
              onClick={() => setAnalysisType(type.value as any)}
              className={`
                p-3 text-left border rounded-lg transition-colors
                ${analysisType === type.value
                  ? 'border-blue-300 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }
              `}
            >
              <div className="font-medium">{type.label}</div>
              <div className="text-xs text-gray-500 mt-1">{type.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* 当前内容信息 */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-medium text-gray-900 mb-2">当前内容</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-500">章节:</span>
            <div className="font-medium">{currentChapter?.title || '未选择'}</div>
          </div>
          <div>
            <span className="text-gray-500">字数:</span>
            <div className="font-medium">{content.length.toLocaleString()}</div>
          </div>
          <div>
            <span className="text-gray-500">段落:</span>
            <div className="font-medium">{content.split('\n\n').length}</div>
          </div>
          <div>
            <span className="text-gray-500">预估阅读:</span>
            <div className="font-medium">{Math.ceil(content.length / 300)} 分钟</div>
          </div>
        </div>
      </div>

      {/* 分析按钮 */}
      <div className="flex justify-center">
        <button
          onClick={handleAnalyze}
          disabled={analysisMutation.isPending || !content.trim()}
          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
        >
          {analysisMutation.isPending ? (
            <ArrowPathIcon className="w-5 h-5 animate-spin" />
          ) : (
            <ChartBarIcon className="w-5 h-5" />
          )}
          <span>
            {analysisMutation.isPending ? '分析中...' : '开始分析'}
          </span>
        </button>
      </div>

      {/* 分析结果 */}
      {analysisResult && (
        <div className="space-y-6">
          {/* 总体评分 */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">分析结果</h3>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">评分:</span>
                <div className={`
                  px-3 py-1 rounded-full text-sm font-medium
                  ${analysisResult.score >= 80 ? 'bg-green-100 text-green-800' :
                    analysisResult.score >= 60 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }
                `}>
                  {analysisResult.score}/100
                </div>
              </div>
            </div>
            
            <div className="prose prose-sm max-w-none">
              <p className="text-gray-700">{analysisResult.analysis}</p>
            </div>
          </div>

          {/* 改进建议 */}
          {analysisResult.suggestions.length > 0 && (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">改进建议</h3>
              <ul className="space-y-3">
                {analysisResult.suggestions.map((suggestion, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mt-0.5">
                      {index + 1}
                    </div>
                    <div className="flex-1 text-gray-700">{suggestion}</div>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* 具体问题 */}
          {analysisResult.issues.length > 0 && (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">发现的问题</h3>
              <div className="space-y-4">
                {analysisResult.issues.map((issue, index) => {
                  const SeverityIcon = getSeverityIcon(issue.severity)
                  return (
                    <div
                      key={index}
                      className={`border rounded-lg p-4 ${getSeverityColor(issue.severity)}`}
                    >
                      <div className="flex items-start space-x-3">
                        <SeverityIcon className="w-5 h-5 mt-0.5" />
                        <div className="flex-1">
                          <div className="font-medium">{issue.type}</div>
                          <div className="text-sm mt-1">{issue.description}</div>
                          <div className="text-sm mt-2 font-medium">建议:</div>
                          <div className="text-sm">{issue.suggestion}</div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 使用提示 */}
      {!analysisResult && !analysisMutation.isPending && (
        <div className="text-center py-8 text-gray-500">
          <ChartBarIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p>选择分析类型并点击"开始分析"</p>
          <p className="text-sm mt-1">AI 将为你的内容提供专业的分析和建议</p>
        </div>
      )}
    </div>
  )
}

export default AIAnalysisPanel
