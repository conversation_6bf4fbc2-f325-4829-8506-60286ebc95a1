import { useState } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  SparklesIcon,
  ArrowPathIcon,
  CheckIcon,

  ClipboardDocumentIcon
} from '@heroicons/react/24/outline'
import { useEditorStore } from '../../stores/editor'
import { useAIConfigStore } from '../../stores/aiConfig'
import { AIService } from '../../services/aiService'
import { characterApi, worldSettingApi, outlineApi } from '../../services/api'
import { handleAIError } from '../../utils/error-handler'
import toast from 'react-hot-toast'

interface AIContinuePanelProps {
  novelId: string
  chapterId?: string
}

export function AIContinuePanel({ novelId, chapterId }: AIContinuePanelProps) {
  const [prompt, setPrompt] = useState('')
  const [length, setLength] = useState<'short' | 'medium' | 'long'>('medium')
  const [style, setStyle] = useState<'narrative' | 'dialogue' | 'description'>('narrative')
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [selectedSuggestion, setSelectedSuggestion] = useState<number | null>(null)
  const [usedSmartSummary, setUsedSmartSummary] = useState(false)

  const { content, currentChapter, currentNovel } = useEditorStore()
  const { getCurrentConfig } = useAIConfigStore()

  // 获取角色数据
  const { data: charactersData } = useQuery({
    queryKey: ['characters', novelId],
    queryFn: () => characterApi.getByNovel(novelId),
    enabled: !!novelId
  })

  // 获取世界设定数据
  const { data: worldSettingsData } = useQuery({
    queryKey: ['world-settings', novelId],
    queryFn: () => worldSettingApi.getByNovel(novelId),
    enabled: !!novelId
  })

  // 获取大纲数据
  const { data: outlinesData } = useQuery({
    queryKey: ['outlines', novelId],
    queryFn: () => outlineApi.getByNovel(novelId),
    enabled: !!novelId
  })

  // 提取实际数据
  const characters = charactersData?.data || []
  const worldSettings = worldSettingsData?.data || []
  const outlines = outlinesData?.data || []

  // AI 续写 mutation
  const continueMutation = useMutation({
    mutationFn: async () => {
      if (!currentNovel || !currentChapter) {
        throw new Error('请先选择章节')
      }

      const aiConfig = getCurrentConfig()

      // 如果内容较长，使用AI进行智能总结
      let contextContent = content
      let usedSummary = false

      if (content.length > 3000) {
        try {
          const summaryResult = await AIService.summarizePreviousContent(
            novelId,
            chapterId || currentChapter.id,
            content
          )

          // 构建结构化的上下文
          contextContent = `
【故事总结】
${summaryResult.summary}

【关键角色状态】
${summaryResult.keyCharacters.map(c => `${c.name}(${c.role}): ${c.currentState}`).join('\n')}

【重要情节点】
${summaryResult.plotPoints.join('\n')}

【未解决线索】
${summaryResult.unresolvedThreads.join('\n')}

【最近事件】
${summaryResult.recentEvents.join('\n')}

【当前章节最新内容】
${content.slice(-1000)}
          `.trim()

          usedSummary = true
        } catch (error) {
          console.warn('前文总结失败，使用简单截取:', error)
          // 如果总结失败，回退到简单截取
          contextContent = content.slice(-2000)
        }
      }

      // 设置是否使用了智能总结
      setUsedSmartSummary(usedSummary)

      const context = {
        novelId,
        chapterId: chapterId || currentChapter.id,
        previousContent: contextContent,
        characters: characters, // 从数据库获取的角色信息
        worldSettings: worldSettings, // 从数据库获取的世界设定
        outline: outlines.length > 0 ? outlines[0] : null, // 使用第一个大纲
        styleGuide: (currentNovel as any).styleSample || undefined
      }

      return AIService.continueWriting({
        context,
        prompt: prompt.trim() || undefined,
        length,
        style,
        // 添加 AI 配置信息
        aiConfig: {
          providerId: aiConfig.providerId,
          modelId: aiConfig.modelId,
          temperature: aiConfig.temperature,
          maxTokens: aiConfig.maxTokens
        }
      })
    },
    onSuccess: (data) => {
      setSuggestions(data.suggestions || [])
      setSelectedSuggestion(null)
      toast.success('AI 续写完成')
    },
    onError: (error: any) => {
      handleAIError(error, {
        fallbackMessage: 'AI 续写失败，请重试',
        toastDuration: 5000,
        showDetails: true,
        detailsDelay: 500
      })
    }
  })

  const handleGenerate = () => {
    if (!currentChapter) {
      toast.error('请先选择一个章节')
      return
    }
    continueMutation.mutate()
  }

  const handleAcceptSuggestion = (index: number) => {
    const suggestion = suggestions[index]
    if (suggestion) {
      // 将建议添加到编辑器内容
      const { setContent } = useEditorStore.getState()
      setContent(content + '\n\n' + suggestion)
      toast.success('内容已添加到编辑器')
      setSuggestions([])
    }
  }

  const handleCopySuggestion = (suggestion: string) => {
    navigator.clipboard.writeText(suggestion)
    toast.success('已复制到剪贴板')
  }

  return (
    <div className="p-6 space-y-6">
      {/* 头部说明 */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <SparklesIcon className="w-5 h-5 text-purple-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-purple-900">AI 智能续写</h3>
            <p className="text-sm text-purple-700 mt-1">
              基于前文内容和写作风格，AI 将为你生成多个续写建议。你可以选择合适的内容直接添加到编辑器中。
            </p>
          </div>
        </div>
      </div>

      {/* 配置选项 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            续写长度
          </label>
          <select
            value={length}
            onChange={(e) => setLength(e.target.value as any)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="short">短 (100-200字)</option>
            <option value="medium">中 (200-500字)</option>
            <option value="long">长 (500-1000字)</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            写作风格
          </label>
          <select
            value={style}
            onChange={(e) => setStyle(e.target.value as any)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="narrative">叙述</option>
            <option value="dialogue">对话</option>
            <option value="description">描写</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            当前章节
          </label>
          <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm text-gray-600">
            {currentChapter?.title || '未选择章节'}
          </div>
        </div>
      </div>

      {/* 当前内容信息 */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-medium text-gray-900 mb-2">当前内容</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-500">字数:</span>
            <div className="font-medium">{content.length.toLocaleString()}</div>
          </div>
          <div>
            <span className="text-gray-500">段落:</span>
            <div className="font-medium">{content.split('\n\n').length}</div>
          </div>
          <div>
            <span className="text-gray-500">角色:</span>
            <div className="font-medium">{characters.length} 个</div>
          </div>
          <div>
            <span className="text-gray-500">世界设定:</span>
            <div className="font-medium">{worldSettings.length} 个</div>
          </div>
        </div>

        {/* 智能总结状态 */}
        {content.length > 3000 && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="flex items-center space-x-2 text-sm">
              <span className="text-gray-500">上下文策略:</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                usedSmartSummary
                  ? 'bg-green-100 text-green-800'
                  : 'bg-blue-100 text-blue-800'
              }`}>
                {usedSmartSummary ? '智能总结' : '简单截取'}
              </span>
              {usedSmartSummary && (
                <span className="text-xs text-gray-500">
                  (已提取关键信息和线索)
                </span>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 提示词输入 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          写作提示 (可选)
        </label>
        <textarea
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="描述你希望接下来发生什么，或者给出写作方向..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          rows={3}
        />
      </div>

      {/* 生成按钮 */}
      <div className="flex justify-center">
        <button
          onClick={handleGenerate}
          disabled={continueMutation.isPending || !currentChapter}
          className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
        >
          {continueMutation.isPending ? (
            <ArrowPathIcon className="w-5 h-5 animate-spin" />
          ) : (
            <SparklesIcon className="w-5 h-5" />
          )}
          <span>
            {continueMutation.isPending ? '生成中...' : '生成续写建议'}
          </span>
        </button>
      </div>

      {/* 生成结果 */}
      {suggestions && suggestions.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">续写建议</h3>
          
          {suggestions.map((suggestion, index) => (
            <div
              key={index}
              className={`
                border rounded-lg p-4 transition-colors cursor-pointer
                ${selectedSuggestion === index
                  ? 'border-purple-300 bg-purple-50'
                  : 'border-gray-200 hover:border-gray-300'
                }
              `}
              onClick={() => setSelectedSuggestion(index)}
            >
              <div className="flex justify-between items-start mb-3">
                <span className="text-sm font-medium text-gray-600">
                  建议 {index + 1}
                </span>
                <div className="flex space-x-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleCopySuggestion(suggestion)
                    }}
                    className="p-1 text-gray-400 hover:text-gray-600"
                    title="复制"
                  >
                    <ClipboardDocumentIcon className="w-4 h-4" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleAcceptSuggestion(index)
                    }}
                    className="p-1 text-green-600 hover:text-green-700"
                    title="采用"
                  >
                    <CheckIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div className="text-gray-800 leading-relaxed whitespace-pre-wrap">
                {suggestion}
              </div>
              
              <div className="mt-3 text-xs text-gray-500">
                约 {suggestion.length} 字
              </div>
            </div>
          ))}

          {/* 批量操作 */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              onClick={() => setSuggestions([])}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              清除建议
            </button>
            <button
              onClick={handleGenerate}
              disabled={continueMutation.isPending}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50"
            >
              重新生成
            </button>
          </div>
        </div>
      )}

      {/* 使用提示 */}
      {(!suggestions || suggestions.length === 0) && !continueMutation.isPending && (
        <div className="text-center py-8 text-gray-500">
          <SparklesIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p>点击"生成续写建议"开始 AI 辅助写作</p>
          <p className="text-sm mt-1">AI 将基于前文内容为你生成多个续写选项</p>
        </div>
      )}
    </div>
  )
}

export default AIContinuePanel
