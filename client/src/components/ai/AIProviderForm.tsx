import { useState, useEffect } from 'react'
import { useMutation } from '@tanstack/react-query'
import { XMarkIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline'
import { 
  AIProviderService, 
  AIProvider, 
  AIProviderModel,
  DEFAULT_PROVIDER_TEMPLATES 
} from '../../services/aiProviderService'
import toast from 'react-hot-toast'

interface AIProviderFormProps {
  provider?: AIProvider | null
  onClose: () => void
  onSuccess: () => void
  embedded?: boolean // 是否嵌入在其他组件中
}

export function AIProviderForm({ provider, onClose, onSuccess, embedded = false }: AIProviderFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    type: '',
    baseUrl: '',
    apiKey: '',
    models: [] as AIProviderModel[],
    config: {},
    isActive: true,
    isDefault: false
  })

  const [selectedTemplate, setSelectedTemplate] = useState('')

  // 编辑模式时填充表单
  useEffect(() => {
    if (provider) {
      setFormData({
        name: provider.name,
        type: provider.type,
        baseUrl: provider.baseUrl,
        apiKey: provider.apiKey || '',
        models: provider.models,
        config: provider.config || {},
        isActive: provider.isActive,
        isDefault: provider.isDefault
      })
    }
  }, [provider])

  // 创建/更新 mutation
  const saveMutation = useMutation({
    mutationFn: async (data: typeof formData) => {
      if (provider) {
        return AIProviderService.updateProvider(provider.id, data)
      } else {
        return AIProviderService.createProvider(data)
      }
    },
    onSuccess: () => {
      toast.success(provider ? 'AI Provider 更新成功' : 'AI Provider 创建成功')
      onSuccess()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error?.message || '保存失败')
    }
  })

  // 应用模板
  const applyTemplate = (templateKey: string) => {
    const template = DEFAULT_PROVIDER_TEMPLATES[templateKey as keyof typeof DEFAULT_PROVIDER_TEMPLATES]
    if (template) {
      setFormData({
        ...formData,
        name: template.name,
        type: template.type,
        baseUrl: template.baseUrl,
        models: template.models,
        config: template.config
      })
      setSelectedTemplate('')
    }
  }

  // 添加模型
  const addModel = () => {
    setFormData({
      ...formData,
      models: [
        ...formData.models,
        { id: '', name: '', type: 'text' }
      ]
    })
  }

  // 更新模型
  const updateModel = (index: number, field: keyof AIProviderModel, value: string) => {
    const newModels = [...formData.models]
    newModels[index] = { ...newModels[index], [field]: value }
    setFormData({ ...formData, models: newModels })
  }

  // 删除模型
  const removeModel = (index: number) => {
    setFormData({
      ...formData,
      models: formData.models.filter((_, i) => i !== index)
    })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // 验证必填字段
    if (!formData.name.trim()) {
      toast.error('请输入 Provider 名称')
      return
    }
    if (!formData.baseUrl.trim()) {
      toast.error('请输入 Base URL')
      return
    }
    if (formData.models.length === 0) {
      toast.error('请至少添加一个模型')
      return
    }

    // 验证模型字段
    for (const model of formData.models) {
      if (!model.id.trim() || !model.name.trim()) {
        toast.error('请完整填写所有模型信息')
        return
      }
    }

    saveMutation.mutate(formData)
  }

  return (
    <div className={embedded
      ? "bg-white border border-gray-200 rounded-lg shadow-lg w-full max-h-[80vh] overflow-hidden"
      : "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    }>
      <div className={embedded
        ? "w-full"
        : "bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden"
      }>
        {/* 头部 */}
        <div className={`flex items-center justify-between border-b border-gray-200 ${embedded ? 'p-4' : 'p-6'}`}>
          <h2 className={`font-semibold text-gray-900 ${embedded ? 'text-lg' : 'text-xl'}`}>
            {provider ? '编辑 AI Provider' : '添加 AI Provider'}
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className={embedded
          ? "overflow-y-auto max-h-[calc(80vh-140px)]"
          : "overflow-y-auto max-h-[calc(90vh-140px)]"
        }>
          <div className={`space-y-6 ${embedded ? 'p-4' : 'p-6'}`}>
            {/* 模板选择 */}
            {!provider && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择模板 (可选)
                </label>
                <select
                  value={selectedTemplate}
                  onChange={(e) => setSelectedTemplate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">自定义配置</option>
                  {Object.entries(DEFAULT_PROVIDER_TEMPLATES).map(([key, template]) => (
                    <option key={key} value={key}>
                      {template.name}
                    </option>
                  ))}
                </select>
                {selectedTemplate && (
                  <button
                    type="button"
                    onClick={() => applyTemplate(selectedTemplate)}
                    className="mt-2 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    应用模板
                  </button>
                )}
              </div>
            )}

            {/* 基本信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Provider 名称 *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="例如: OpenAI"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Provider 类型
                </label>
                <input
                  type="text"
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="例如: openai"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Base URL *
              </label>
              <input
                type="url"
                value={formData.baseUrl}
                onChange={(e) => setFormData({ ...formData, baseUrl: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://api.openai.com/v1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                API Key (可选)
              </label>
              <input
                type="password"
                value={formData.apiKey}
                onChange={(e) => setFormData({ ...formData, apiKey: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="sk-..."
              />
            </div>

            {/* 模型配置 */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium text-gray-700">
                  支持的模型 *
                </label>
                <button
                  type="button"
                  onClick={addModel}
                  className="flex items-center space-x-1 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  <PlusIcon className="w-4 h-4" />
                  <span>添加模型</span>
                </button>
              </div>

              <div className="space-y-3">
                {formData.models.map((model, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-md">
                    <input
                      type="text"
                      value={model.id}
                      onChange={(e) => updateModel(index, 'id', e.target.value)}
                      placeholder="模型 ID"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <input
                      type="text"
                      value={model.name}
                      onChange={(e) => updateModel(index, 'name', e.target.value)}
                      placeholder="显示名称"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <select
                      value={model.type}
                      onChange={(e) => updateModel(index, 'type', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="text">文本</option>
                      <option value="image">图像</option>
                      <option value="multimodal">多模态</option>
                    </select>
                    <button
                      type="button"
                      onClick={() => removeModel(index)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-md"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* 状态设置 */}
            <div className="flex items-center space-x-6">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">激活此 Provider</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.isDefault}
                  onChange={(e) => setFormData({ ...formData, isDefault: e.target.checked })}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">设为默认 Provider</span>
              </label>
            </div>
          </div>

          {/* 底部按钮 */}
          <div className={`flex justify-end space-x-3 border-t border-gray-200 bg-gray-50 ${embedded ? 'p-4' : 'p-6'}`}>
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={saveMutation.isPending}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {saveMutation.isPending ? '保存中...' : (provider ? '更新' : '创建')}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default AIProviderForm
