import { useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { PhotoIcon, ArrowPathIcon } from '@heroicons/react/24/outline'
import { AIService } from '../../services/aiService'
import toast from 'react-hot-toast'

interface AIImagePanelProps {
  novelId: string
}

export function AIImagePanel({ novelId: _novelId }: AIImagePanelProps) {
  const [prompt, setPrompt] = useState('')
  const [imageType, setImageType] = useState<'scene' | 'character' | 'cover' | 'concept'>('scene')
  const [style, setStyle] = useState<'realistic' | 'anime' | 'fantasy' | 'sci-fi' | 'watercolor'>('fantasy')
  const [aspectRatio, setAspectRatio] = useState<'1:1' | '16:9' | '9:16' | '4:3'>('16:9')
  const [generatedImage, setGeneratedImage] = useState<string | null>(null)

  const imageMutation = useMutation({
    mutationFn: async () => {
      if (!prompt.trim()) {
        throw new Error('请输入图像描述')
      }

      return AIService.generateImage({
        prompt: prompt.trim(),
        type: imageType,
        style,
        aspectRatio
      })
    },
    onSuccess: (data) => {
      setGeneratedImage(data.imageUrl)
      toast.success('图像生成完成')
    },
    onError: (error) => {
      console.error('图像生成失败:', error)
      toast.error('图像生成失败，请重试')
    }
  })

  return (
    <div className="p-6 space-y-6">
      <div className="bg-pink-50 border border-pink-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <PhotoIcon className="w-5 h-5 text-pink-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-pink-900">AI 插图生成</h3>
            <p className="text-sm text-pink-700 mt-1">
              基于文字描述生成高质量的插图，包括场景、角色、封面等。
            </p>
          </div>
        </div>
      </div>

      {/* 图像描述 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          图像描述
        </label>
        <textarea
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="详细描述你想要生成的图像，例如：一个古代的图书馆，书架高耸入云，阳光从彩色玻璃窗洒进来..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
          rows={4}
        />
      </div>

      {/* 配置选项 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            图像类型
          </label>
          <select
            value={imageType}
            onChange={(e) => setImageType(e.target.value as any)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
          >
            <option value="scene">场景</option>
            <option value="character">角色</option>
            <option value="cover">封面</option>
            <option value="concept">概念图</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            艺术风格
          </label>
          <select
            value={style}
            onChange={(e) => setStyle(e.target.value as any)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
          >
            <option value="realistic">写实</option>
            <option value="anime">动漫</option>
            <option value="fantasy">奇幻</option>
            <option value="sci-fi">科幻</option>
            <option value="watercolor">水彩</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            画面比例
          </label>
          <select
            value={aspectRatio}
            onChange={(e) => setAspectRatio(e.target.value as any)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
          >
            <option value="1:1">正方形 (1:1)</option>
            <option value="16:9">横屏 (16:9)</option>
            <option value="9:16">竖屏 (9:16)</option>
            <option value="4:3">传统 (4:3)</option>
          </select>
        </div>
      </div>

      {/* 生成按钮 */}
      <div className="flex justify-center">
        <button
          onClick={() => imageMutation.mutate()}
          disabled={imageMutation.isPending || !prompt.trim()}
          className="px-6 py-3 bg-pink-600 text-white rounded-lg hover:bg-pink-700 disabled:opacity-50 flex items-center space-x-2"
        >
          {imageMutation.isPending ? (
            <ArrowPathIcon className="w-5 h-5 animate-spin" />
          ) : (
            <PhotoIcon className="w-5 h-5" />
          )}
          <span>
            {imageMutation.isPending ? '生成中...' : '生成图像'}
          </span>
        </button>
      </div>

      {/* 生成结果 */}
      {generatedImage && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">生成结果</h3>
          <div className="text-center">
            <img
              src={generatedImage}
              alt="AI 生成的图像"
              className="max-w-full h-auto rounded-lg shadow-lg mx-auto"
            />
            <div className="mt-4 flex justify-center space-x-3">
              <button
                onClick={() => {
                  const link = document.createElement('a')
                  link.href = generatedImage
                  link.download = 'ai-generated-image.png'
                  link.click()
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                下载图像
              </button>
              <button
                onClick={() => imageMutation.mutate()}
                className="px-4 py-2 bg-pink-600 text-white rounded-md hover:bg-pink-700"
              >
                重新生成
              </button>
            </div>
          </div>
        </div>
      )}

      {!generatedImage && !imageMutation.isPending && (
        <div className="text-center py-8 text-gray-500">
          <PhotoIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p>输入图像描述并点击"生成图像"</p>
        </div>
      )}
    </div>
  )
}

export default AIImagePanel
