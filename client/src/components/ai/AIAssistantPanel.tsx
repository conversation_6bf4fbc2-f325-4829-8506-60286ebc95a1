import { useState } from 'react'
import { 
  SparklesIcon, 
  LightBulbIcon, 
  DocumentTextIcon,
  PhotoIcon,
  ChartBarIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline'
import { AIContinuePanel } from './AIContinuePanel'
import { AIAnalysisPanel } from './AIAnalysisPanel'
import { AISummaryPanel } from './AISummaryPanel'
import { AIImagePanel } from './AIImagePanel'
import { AIOutlinePanel } from './AIOutlinePanel'
import { AISettingsPanel } from './AISettingsPanel'

type AITab = 'continue' | 'analysis' | 'summary' | 'image' | 'outline' | 'settings'

interface AIAssistantPanelProps {
  novelId: string
  chapterId?: string
  isOpen: boolean
  onClose: () => void
}

export function AIAssistantPanel({ 
  novelId, 
  chapterId, 
  isOpen, 
  onClose 
}: AIAssistantPanelProps) {
  const [activeTab, setActiveTab] = useState<AITab>('continue')

  if (!isOpen) return null

  const tabs = [
    {
      id: 'continue' as AITab,
      name: 'AI 续写',
      icon: SparklesIcon,
      description: '智能内容生成'
    },
    {
      id: 'analysis' as AITab,
      name: '内容分析',
      icon: ChartBarIcon,
      description: '风格和情节分析'
    },
    {
      id: 'summary' as AITab,
      name: '智能总结',
      icon: DocumentTextIcon,
      description: '前文总结和线索跟踪'
    },
    {
      id: 'image' as AITab,
      name: '插图生成',
      icon: PhotoIcon,
      description: '场景和角色插图'
    },
    {
      id: 'outline' as AITab,
      name: '大纲助手',
      icon: LightBulbIcon,
      description: '智能大纲生成'
    },
    {
      id: 'settings' as AITab,
      name: '设置',
      icon: Cog6ToothIcon,
      description: 'AI 参数配置'
    }
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case 'continue':
        return <AIContinuePanel novelId={novelId} chapterId={chapterId} />
      case 'analysis':
        return <AIAnalysisPanel novelId={novelId} chapterId={chapterId} />
      case 'summary':
        return <AISummaryPanel novelId={novelId} />
      case 'image':
        return <AIImagePanel novelId={novelId} />
      case 'outline':
        return <AIOutlinePanel novelId={novelId} />
      case 'settings':
        return <AISettingsPanel />
      default:
        return null
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <SparklesIcon className="w-6 h-6 text-purple-600" />
            <h2 className="text-xl font-semibold text-gray-900">AI 写作助手</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* 侧边栏标签 */}
          <div className="w-64 bg-gray-50 border-r border-gray-200 p-4">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      w-full flex items-start space-x-3 p-3 rounded-lg text-left transition-colors
                      ${activeTab === tab.id
                        ? 'bg-purple-100 text-purple-700 border border-purple-200'
                        : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                      }
                    `}
                  >
                    <Icon className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-medium">{tab.name}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {tab.description}
                      </div>
                    </div>
                  </button>
                )
              })}
            </nav>

            {/* 快捷操作 */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-900 mb-3">快捷操作</h3>
              <div className="space-y-2">
                <button className="w-full text-left text-sm text-gray-600 hover:text-gray-900 p-2 hover:bg-gray-100 rounded">
                  📝 快速续写
                </button>
                <button className="w-full text-left text-sm text-gray-600 hover:text-gray-900 p-2 hover:bg-gray-100 rounded">
                  🔍 内容检查
                </button>
                <button className="w-full text-left text-sm text-gray-600 hover:text-gray-900 p-2 hover:bg-gray-100 rounded">
                  🎨 生成插图
                </button>
              </div>
            </div>
          </div>

          {/* 主内容区 */}
          <div className="flex-1 overflow-auto">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  )
}

export default AIAssistantPanel
