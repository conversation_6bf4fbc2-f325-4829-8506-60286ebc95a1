import { useMutation, useQuery } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'
import { authApi, userApi, handleApiError } from '../services/api'
import { useAuthStore } from '../stores/auth'

// 登录数据类型
interface LoginData {
  email: string
  password: string
}

// 注册数据类型
interface RegisterData {
  username: string
  email: string
  password: string
}

// 更新用户数据类型
interface UpdateUserData {
  username?: string
  email?: string
  password?: string
  avatar?: string
  settings?: Record<string, any>
}

/**
 * 认证相关的mutations
 */
export function useAuthMutations() {
  const navigate = useNavigate()
  const { login, logout, setLoading, updateUser } = useAuthStore()

  // 登录mutation
  const loginMutation = useMutation({
    mutationFn: (data: LoginData) => authApi.login(data),
    onMutate: () => {
      setLoading(true)
    },
    onSuccess: (response) => {
      if (response.success && response.data) {
        const { user, accessToken, refreshToken } = response.data
        login(user, accessToken, refreshToken)
        toast.success('登录成功')
        navigate('/dashboard')
      }
    },
    onError: (error) => {
      const message = handleApiError(error)
      toast.error(message)
    },
    onSettled: () => {
      setLoading(false)
    },
  })

  // 注册mutation
  const registerMutation = useMutation({
    mutationFn: (data: RegisterData) => authApi.register(data),
    onMutate: () => {
      setLoading(true)
    },
    onSuccess: (response) => {
      if (response.success && response.data) {
        const { user, accessToken, refreshToken } = response.data
        login(user, accessToken, refreshToken)
        toast.success('注册成功')
        navigate('/dashboard')
      }
    },
    onError: (error) => {
      const message = handleApiError(error)
      toast.error(message)
    },
    onSettled: () => {
      setLoading(false)
    },
  })

  // 登出mutation
  const logoutMutation = useMutation({
    mutationFn: () => authApi.logout(),
    onMutate: () => {
      setLoading(true)
    },
    onSuccess: () => {
      logout()
      toast.success('已安全登出')
      navigate('/login')
    },
    onError: (error) => {
      // 即使服务器登出失败，也要清除本地状态
      logout()
      toast.success('已安全登出')
      navigate('/login')
      console.error('登出请求失败:', error)
    },
    onSettled: () => {
      setLoading(false)
    },
  })

  // 更新用户信息mutation
  const updateUserMutation = useMutation({
    mutationFn: (data: UpdateUserData) => userApi.updateProfile(data),
    onSuccess: (response) => {
      if (response.success && response.data) {
        updateUser(response.data.user)
        toast.success('用户信息更新成功')
      }
    },
    onError: (error) => {
      const message = handleApiError(error)
      toast.error(message)
    },
  })

  // 删除账户mutation
  const deleteAccountMutation = useMutation({
    mutationFn: () => userApi.deleteAccount(),
    onSuccess: () => {
      logout()
      toast.success('账户已删除')
      navigate('/login')
    },
    onError: (error) => {
      const message = handleApiError(error)
      toast.error(message)
    },
  })

  return {
    loginMutation,
    registerMutation,
    logoutMutation,
    updateUserMutation,
    deleteAccountMutation,
  }
}

/**
 * 获取用户信息的query
 */
export function useUserProfile() {
  const { isAuthenticated, updateUser } = useAuthStore()

  return useQuery({
    queryKey: ['user', 'profile'],
    queryFn: async () => {
      const response = await userApi.getProfile()
      if (response.success && response.data) {
        updateUser(response.data.user)
        return response.data.user
      }
      throw new Error('获取用户信息失败')
    },
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5分钟
    retry: 1,
  })
}

/**
 * 认证状态初始化hook
 */
export function useAuthInitialization() {
  const { initializeAuth } = useAuthStore()

  // 在应用启动时初始化认证状态
  React.useEffect(() => {
    initializeAuth()
  }, [initializeAuth])
}

/**
 * 路由保护hook
 */
export function useAuthGuard() {
  const { isAuthenticated, isLoading } = useAuthStore()
  const navigate = useNavigate()

  React.useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/login')
    }
  }, [isAuthenticated, isLoading, navigate])

  return { isAuthenticated, isLoading }
}

/**
 * 登录状态检查hook
 */
export function useLoginRedirect() {
  const { isAuthenticated } = useAuthStore()
  const navigate = useNavigate()

  React.useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard')
    }
  }, [isAuthenticated, navigate])

  return { isAuthenticated }
}

/**
 * 表单验证hook
 */
export function useAuthValidation() {
  const validateEmail = (email: string): string | null => {
    if (!email) {
      return '邮箱不能为空'
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return '邮箱格式不正确'
    }
    return null
  }

  const validatePassword = (password: string): string | null => {
    if (!password) {
      return '密码不能为空'
    }
    if (password.length < 8) {
      return '密码长度至少8位'
    }
    const hasLetter = /[a-zA-Z]/.test(password)
    const hasNumber = /\d/.test(password)
    if (!hasLetter || !hasNumber) {
      return '密码必须包含至少一个字母和一个数字'
    }
    return null
  }

  const validateUsername = (username: string): string | null => {
    if (!username) {
      return '用户名不能为空'
    }
    if (username.length < 3 || username.length > 50) {
      return '用户名长度必须在3-50字符之间'
    }
    const usernameRegex = /^[\w\u4e00-\u9fa5]+$/
    if (!usernameRegex.test(username)) {
      return '用户名只能包含字母、数字、下划线和中文字符'
    }
    return null
  }

  const validateConfirmPassword = (password: string, confirmPassword: string): string | null => {
    if (!confirmPassword) {
      return '请确认密码'
    }
    if (password !== confirmPassword) {
      return '两次输入的密码不一致'
    }
    return null
  }

  return {
    validateEmail,
    validatePassword,
    validateUsername,
    validateConfirmPassword,
  }
}

// 导入React（如果需要）
import React from 'react'
