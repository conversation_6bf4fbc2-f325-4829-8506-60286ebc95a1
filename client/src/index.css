@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 自定义字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Crimson+Text:ital,wght@0,400;0,600;1,400&display=swap');

/* 基础样式 */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
  
  /* 选择文本样式 */
  ::selection {
    @apply bg-primary-200 text-primary-900;
  }
}

/* 组件样式 */
@layer components {
  /* 按钮基础样式 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
  }
  
  .btn-ghost {
    @apply btn text-gray-600 hover:bg-gray-100 focus:ring-gray-500;
  }
  
  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  /* 输入框样式 */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .input-error {
    @apply input border-red-300 focus:ring-red-500 focus:border-red-500;
  }
  
  /* 卡片样式 */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }
  
  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  /* 文本样式 */
  .text-heading {
    @apply text-2xl font-semibold text-gray-900;
  }
  
  .text-subheading {
    @apply text-lg font-medium text-gray-700;
  }
  
  .text-body {
    @apply text-sm text-gray-600;
  }
  
  .text-caption {
    @apply text-xs text-gray-500;
  }
  
  /* 写作相关样式 */
  .prose-editor {
    font-family: 'Crimson Text', Georgia, serif;
    @apply text-lg leading-relaxed text-gray-800;
  }
  
  .prose-editor h1 {
    @apply text-3xl font-semibold mb-4 text-gray-900;
  }
  
  .prose-editor h2 {
    @apply text-2xl font-semibold mb-3 text-gray-900;
  }
  
  .prose-editor h3 {
    @apply text-xl font-semibold mb-2 text-gray-900;
  }
  
  .prose-editor p {
    @apply mb-4;
  }
  
  .prose-editor blockquote {
    @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4;
  }
  
  /* 侧边栏样式 */
  .sidebar {
    @apply bg-white border-r border-gray-200 h-full overflow-y-auto;
  }
  
  .sidebar-header {
    @apply px-4 py-3 border-b border-gray-200 bg-gray-50;
  }
  
  .sidebar-content {
    @apply p-4;
  }
  
  /* 导航样式 */
  .nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors;
  }
  
  .nav-item-active {
    @apply nav-item bg-primary-100 text-primary-700;
  }
  
  .nav-item-inactive {
    @apply nav-item text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  }
}

/* 工具类 */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* 动画工具类 */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 响应式断点提示（开发时使用） */
@media (max-width: 640px) {
  .debug-breakpoint::before {
    content: 'sm';
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .debug-breakpoint::before {
    content: 'md';
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .debug-breakpoint::before {
    content: 'lg';
  }
}

@media (min-width: 1025px) {
  .debug-breakpoint::before {
    content: 'xl';
  }
}
