import { describe, it, expect, beforeEach, vi } from 'vitest'
import axios from 'axios'
import { apiClient, authApi } from './api'

// Mock axios
vi.mock('axios')
const mockedAxios = vi.mocked(axios)

// Mock useAuthStore
const mockLogout = vi.fn()
const mockUpdateTokens = vi.fn()
const mockGetAccessToken = vi.fn()
const mockGetRefreshToken = vi.fn()

vi.mock('../stores/auth', () => ({
  useAuthStore: {
    getState: () => ({
      logout: mockLogout,
      updateTokens: mockUpdateTokens,
      accessToken: mockGetAccessToken(),
      refreshToken: mockGetRefreshToken(),
    }),
  },
}))

describe('API服务', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockGetAccessToken.mockReturnValue('test-access-token')
    mockGetRefreshToken.mockReturnValue('test-refresh-token')
  })

  describe('apiClient配置', () => {
    it('应该有正确的基础URL', () => {
      expect(apiClient.defaults.baseURL).toBe('/api')
    })

    it('应该有正确的默认headers', () => {
      expect(apiClient.defaults.headers.common['Content-Type']).toBe('application/json')
    })

    it('应该有正确的超时设置', () => {
      expect(apiClient.defaults.timeout).toBe(10000)
    })
  })

  describe('请求拦截器', () => {
    it('应该在请求中添加Authorization头', async () => {
      const mockRequest = {
        headers: {},
        url: '/test',
      }

      // 模拟请求拦截器
      const interceptor = apiClient.interceptors.request.handlers[0]
      if (interceptor && interceptor.fulfilled) {
        const result = await interceptor.fulfilled(mockRequest)
        expect(result.headers.Authorization).toBe('Bearer test-access-token')
      }
    })

    it('应该在没有token时不添加Authorization头', async () => {
      mockGetAccessToken.mockReturnValue(null)
      
      const mockRequest = {
        headers: {},
        url: '/test',
      }

      const interceptor = apiClient.interceptors.request.handlers[0]
      if (interceptor && interceptor.fulfilled) {
        const result = await interceptor.fulfilled(mockRequest)
        expect(result.headers.Authorization).toBeUndefined()
      }
    })
  })

  describe('响应拦截器', () => {
    it('应该直接返回成功响应', async () => {
      const mockResponse = {
        data: { success: true, data: { message: 'test' } },
        status: 200,
      }

      const interceptor = apiClient.interceptors.response.handlers[0]
      if (interceptor && interceptor.fulfilled) {
        const result = await interceptor.fulfilled(mockResponse)
        expect(result).toBe(mockResponse)
      }
    })

    it('应该处理401错误并尝试刷新token', async () => {
      const mockError = {
        response: {
          status: 401,
          data: { error: { code: 'TOKEN_EXPIRED' } },
        },
        config: {
          url: '/test',
          headers: {},
        },
      }

      // Mock刷新token成功
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: true,
          data: {
            accessToken: 'new-access-token',
            refreshToken: 'new-refresh-token',
          },
        },
      })

      // Mock重试请求成功
      mockedAxios.request.mockResolvedValueOnce({
        data: { success: true, data: { message: 'retry success' } },
      })

      const interceptor = apiClient.interceptors.response.handlers[0]
      if (interceptor && interceptor.rejected) {
        await interceptor.rejected(mockError)
        
        expect(mockedAxios.post).toHaveBeenCalledWith('/auth/refresh', {
          refreshToken: 'test-refresh-token',
        })
        expect(mockUpdateTokens).toHaveBeenCalledWith('new-access-token', 'new-refresh-token')
        expect(mockedAxios.request).toHaveBeenCalledWith({
          ...mockError.config,
          headers: {
            ...mockError.config.headers,
            Authorization: 'Bearer new-access-token',
          },
        })
      }
    })

    it('应该在刷新token失败时登出用户', async () => {
      const mockError = {
        response: {
          status: 401,
          data: { error: { code: 'TOKEN_EXPIRED' } },
        },
        config: {
          url: '/test',
          headers: {},
        },
      }

      // Mock刷新token失败
      mockedAxios.post.mockRejectedValueOnce(new Error('Refresh failed'))

      const interceptor = apiClient.interceptors.response.handlers[0]
      if (interceptor && interceptor.rejected) {
        try {
          await interceptor.rejected(mockError)
        } catch (error) {
          expect(mockLogout).toHaveBeenCalled()
        }
      }
    })

    it('应该直接抛出非401错误', async () => {
      const mockError = {
        response: {
          status: 500,
          data: { error: { message: 'Internal server error' } },
        },
      }

      const interceptor = apiClient.interceptors.response.handlers[0]
      if (interceptor && interceptor.rejected) {
        await expect(interceptor.rejected(mockError)).rejects.toBe(mockError)
      }
    })
  })

  describe('authApi', () => {
    beforeEach(() => {
      mockedAxios.post.mockClear()
    })

    describe('register', () => {
      it('应该发送注册请求', async () => {
        const userData = {
          username: 'testuser',
          email: '<EMAIL>',
          password: 'password123',
        }

        const mockResponse = {
          data: {
            success: true,
            data: {
              user: { id: '1', username: 'testuser', email: '<EMAIL>' },
              accessToken: 'access-token',
              refreshToken: 'refresh-token',
            },
          },
        }

        mockedAxios.post.mockResolvedValueOnce(mockResponse)

        const result = await authApi.register(userData)

        expect(mockedAxios.post).toHaveBeenCalledWith('/auth/register', userData)
        expect(result).toBe(mockResponse.data)
      })
    })

    describe('login', () => {
      it('应该发送登录请求', async () => {
        const credentials = {
          email: '<EMAIL>',
          password: 'password123',
        }

        const mockResponse = {
          data: {
            success: true,
            data: {
              user: { id: '1', username: 'testuser', email: '<EMAIL>' },
              accessToken: 'access-token',
              refreshToken: 'refresh-token',
            },
          },
        }

        mockedAxios.post.mockResolvedValueOnce(mockResponse)

        const result = await authApi.login(credentials)

        expect(mockedAxios.post).toHaveBeenCalledWith('/auth/login', credentials)
        expect(result).toBe(mockResponse.data)
      })
    })

    describe('logout', () => {
      it('应该发送登出请求', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: { message: '登出成功' },
          },
        }

        mockedAxios.post.mockResolvedValueOnce(mockResponse)

        const result = await authApi.logout()

        expect(mockedAxios.post).toHaveBeenCalledWith('/auth/logout')
        expect(result).toBe(mockResponse.data)
      })
    })

    describe('refreshToken', () => {
      it('应该发送刷新token请求', async () => {
        const refreshToken = 'refresh-token-123'

        const mockResponse = {
          data: {
            success: true,
            data: {
              user: { id: '1', username: 'testuser', email: '<EMAIL>' },
              accessToken: 'new-access-token',
              refreshToken: 'new-refresh-token',
            },
          },
        }

        mockedAxios.post.mockResolvedValueOnce(mockResponse)

        const result = await authApi.refreshToken(refreshToken)

        expect(mockedAxios.post).toHaveBeenCalledWith('/auth/refresh', { refreshToken })
        expect(result).toBe(mockResponse.data)
      })
    })
  })
})
