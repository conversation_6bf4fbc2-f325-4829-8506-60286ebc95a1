// AI Provider 管理服务
import { apiClient } from './api'

export interface AIProviderModel {
  id: string
  name: string
  type: 'text' | 'image' | 'multimodal'
}

export interface AIProvider {
  id: string
  name: string
  type: string
  baseUrl: string
  apiKey?: string
  models: AIProviderModel[]
  config?: Record<string, any>
  isActive: boolean
  isDefault: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateAIProviderRequest {
  name: string
  type: string
  baseUrl: string
  apiKey?: string
  models: AIProviderModel[]
  config?: Record<string, any>
  isActive?: boolean
  isDefault?: boolean
}

export interface UpdateAIProviderRequest extends Partial<CreateAIProviderRequest> {}

export interface AIProviderTestResult {
  success: boolean
  latency: number
  models: AIProviderModel[]
  message: string
}

// 默认 AI Provider 模板
export const DEFAULT_PROVIDER_TEMPLATES = {
  openai: {
    name: 'OpenAI',
    type: 'openai',
    baseUrl: 'https://api.openai.com/v1',
    models: [
      { id: 'gpt-4', name: 'GPT-4', type: 'text' as const },
      { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', type: 'text' as const },
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', type: 'text' as const },
      { id: 'dall-e-3', name: 'DALL-E 3', type: 'image' as const }
    ],
    config: {
      timeout: 30000,
      maxTokens: 4000,
      temperature: 0.7
    }
  },
  ollama: {
    name: 'Ollama',
    type: 'ollama',
    baseUrl: 'http://localhost:11434/v1',
    models: [
      { id: 'llama3.2', name: 'Llama 3.2', type: 'text' as const },
      { id: 'qwen2.5', name: 'Qwen 2.5', type: 'text' as const },
      { id: 'mistral', name: 'Mistral', type: 'text' as const }
    ],
    config: {
      timeout: 60000,
      maxTokens: 8000,
      temperature: 0.8,
      requiresApiKey: false
    }
  },
  gemini: {
    name: 'Google Gemini',
    type: 'gemini',
    baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
    models: [
      { id: 'gemini-2.5-pro', name: 'Gemini 2.5 Pro', type: 'text' as const },
      { id: 'gemini-2.5-flash', name: 'Gemini 2.5 Flash', type: 'text' as const },
      { id: 'gemini-pro-vision', name: 'Gemini Pro Vision', type: 'multimodal' as const }
    ],
    config: {
      timeout: 30000,
      maxTokens: 8000,
      temperature: 0.7
    }
  },
  anthropic: {
    name: 'Anthropic Claude',
    type: 'anthropic',
    baseUrl: 'https://api.anthropic.com/v1',
    models: [
      { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet', type: 'text' as const },
      { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', type: 'text' as const }
    ],
    config: {
      timeout: 30000,
      maxTokens: 4000,
      temperature: 0.7
    }
  },
  'azure-openai': {
    name: 'Azure OpenAI',
    type: 'azure-openai',
    baseUrl: 'https://your-resource.openai.azure.com/openai/deployments',
    models: [
      { id: 'gpt-4', name: 'GPT-4 (Azure)', type: 'text' as const },
      { id: 'gpt-35-turbo', name: 'GPT-3.5 Turbo (Azure)', type: 'text' as const }
    ],
    config: {
      timeout: 30000,
      maxTokens: 4000,
      temperature: 0.7,
      apiVersion: '2024-02-01'
    }
  },
  cohere: {
    name: 'Cohere',
    type: 'cohere',
    baseUrl: 'https://api.cohere.ai/v1',
    models: [
      { id: 'command-r-plus', name: 'Command R+', type: 'text' as const },
      { id: 'command-r', name: 'Command R', type: 'text' as const }
    ],
    config: {
      timeout: 30000,
      maxTokens: 4000,
      temperature: 0.7
    }
  }
}

export class AIProviderService {
  /**
   * 获取所有 AI Providers
   */
  static async getProviders(): Promise<AIProvider[]> {
    const response = await apiClient.get('/ai-providers')
    return response.data.data || []
  }

  /**
   * 获取激活的 AI Providers
   */
  static async getActiveProviders(): Promise<AIProvider[]> {
    const response = await apiClient.get('/ai-providers/active')
    return response.data.data || []
  }

  /**
   * 获取单个 AI Provider
   */
  static async getProvider(id: string): Promise<AIProvider> {
    const response = await apiClient.get(`/ai-providers/${id}`)
    return response.data.data
  }

  /**
   * 创建 AI Provider
   */
  static async createProvider(data: CreateAIProviderRequest): Promise<AIProvider> {
    const response = await apiClient.post('/ai-providers', data)
    return response.data.data
  }

  /**
   * 更新 AI Provider
   */
  static async updateProvider(id: string, data: UpdateAIProviderRequest): Promise<AIProvider> {
    const response = await apiClient.put(`/ai-providers/${id}`, data)
    return response.data.data
  }

  /**
   * 删除 AI Provider
   */
  static async deleteProvider(id: string): Promise<void> {
    await apiClient.delete(`/ai-providers/${id}`)
  }

  /**
   * 测试 AI Provider 连接
   */
  static async testProvider(id: string): Promise<AIProviderTestResult> {
    const response = await apiClient.post(`/ai-providers/${id}/test`)
    return response.data.data
  }

  /**
   * 设置默认 AI Provider
   */
  static async setDefaultProvider(id: string): Promise<AIProvider> {
    const response = await apiClient.post(`/ai-providers/${id}/set-default`)
    return response.data.data
  }

  /**
   * 根据类型获取默认配置模板
   */
  static getProviderTemplate(type: keyof typeof DEFAULT_PROVIDER_TEMPLATES) {
    return DEFAULT_PROVIDER_TEMPLATES[type]
  }

  /**
   * 获取所有可用的 Provider 类型
   */
  static getAvailableTypes() {
    return Object.keys(DEFAULT_PROVIDER_TEMPLATES).map(key => ({
      id: key,
      name: DEFAULT_PROVIDER_TEMPLATES[key as keyof typeof DEFAULT_PROVIDER_TEMPLATES].name,
      type: key
    }))
  }
}

export default AIProviderService
