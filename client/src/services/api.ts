import axios, { AxiosError, AxiosResponse } from 'axios'
import { useAuthStore } from '../stores/auth'
import type { ApiResponse, User } from '@shared/index.js'

// 创建axios实例
export const apiClient = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 添加认证token
apiClient.interceptors.request.use(
  (config) => {
    const { accessToken } = useAuthStore.getState()
    
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理token刷新
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  async (error: AxiosError) => {
    const originalRequest = error.config
    
    // 如果是401错误且不是刷新token请求
    if (
      error.response?.status === 401 &&
      originalRequest &&
      !originalRequest.url?.includes('/auth/refresh')
    ) {
      try {
        const { refreshToken, updateTokens, logout } = useAuthStore.getState()
        
        if (!refreshToken) {
          logout()
          return Promise.reject(error)
        }

        // 尝试刷新token
        const response = await apiClient.post('/auth/refresh', {
          refreshToken,
        })

        const { accessToken: newAccessToken, refreshToken: newRefreshToken } = response.data.data
        
        // 更新token
        updateTokens(newAccessToken, newRefreshToken)
        
        // 重试原请求
        if (originalRequest.headers) {
          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`
        }
        
        return apiClient.request(originalRequest)
      } catch (refreshError) {
        // 刷新token失败，登出用户
        useAuthStore.getState().logout()
        return Promise.reject(refreshError)
      }
    }
    
    return Promise.reject(error)
  }
)

// 认证相关API
export const authApi = {
  /**
   * 用户注册
   */
  register: async (userData: {
    username: string
    email: string
    password: string
  }): Promise<ApiResponse<{
    user: Omit<User, 'password'>
    accessToken: string
    refreshToken: string
  }>> => {
    const response = await apiClient.post('/auth/register', userData)
    return response.data
  },

  /**
   * 用户登录
   */
  login: async (credentials: {
    email: string
    password: string
  }): Promise<ApiResponse<{
    user: Omit<User, 'password'>
    accessToken: string
    refreshToken: string
  }>> => {
    const response = await apiClient.post('/auth/login', credentials)
    return response.data
  },

  /**
   * 用户登出
   */
  logout: async (): Promise<ApiResponse<{ message: string }>> => {
    const response = await apiClient.post('/auth/logout')
    return response.data
  },

  /**
   * 刷新访问令牌
   */
  refreshToken: async (refreshToken: string): Promise<ApiResponse<{
    user: Omit<User, 'password'>
    accessToken: string
    refreshToken: string
  }>> => {
    const response = await apiClient.post('/auth/refresh', { refreshToken })
    return response.data
  },
}

// 用户相关API
export const userApi = {
  /**
   * 获取当前用户信息
   */
  getProfile: async (): Promise<ApiResponse<{ user: Omit<User, 'password'> }>> => {
    const response = await apiClient.get('/users/profile')
    return response.data
  },

  /**
   * 更新用户信息
   */
  updateProfile: async (userData: {
    username?: string
    email?: string
    password?: string
    avatar?: string
    settings?: Record<string, any>
  }): Promise<ApiResponse<{ user: Omit<User, 'password'> }>> => {
    const response = await apiClient.put('/users/profile', userData)
    return response.data
  },

  /**
   * 删除用户账户
   */
  deleteAccount: async (): Promise<ApiResponse<{ message: string }>> => {
    const response = await apiClient.delete('/users/profile')
    return response.data
  },
}

// AI相关API
export const aiApi = {
  /**
   * 获取AI模型状态
   */
  getModels: async (): Promise<ApiResponse<{ providers: any[] }>> => {
    const response = await apiClient.get('/ai/models')
    return response.data
  },

  /**
   * AI内容生成
   */
  generate: async (request: {
    type: 'continue' | 'rewrite' | 'outline' | 'character' | 'summary' | 'image'
    prompt: string
    context?: string
    parameters?: Record<string, any>
  }): Promise<ApiResponse<{ result: any }>> => {
    const response = await apiClient.post('/ai/generate', request)
    return response.data
  },

  /**
   * 分析文本风格
   */
  analyzeStyle: async (text: string): Promise<ApiResponse<{ analysis: any }>> => {
    const response = await apiClient.post('/ai/analyze-style', { text })
    return response.data
  },

  /**
   * 生成故事大纲
   */
  generateOutline: async (
    synopsis: string,
    styleFeatures?: Record<string, any>
  ): Promise<ApiResponse<{ outline: any }>> => {
    const response = await apiClient.post('/ai/generate-outline', {
      synopsis,
      styleFeatures,
    })
    return response.data
  },

  /**
   * 总结文本内容
   */
  summarize: async (text: string): Promise<ApiResponse<{ summary: any }>> => {
    const response = await apiClient.post('/ai/summarize', { text })
    return response.data
  },

  /**
   * 智能续写
   */
  smartContinue: async (
    content: string,
    context?: {
      previousChapters?: string[]
      characterProfiles?: Record<string, any>
      worldSettings?: Record<string, any>
      styleGuidelines?: Record<string, any>
    }
  ): Promise<ApiResponse<{ result: any }>> => {
    const response = await apiClient.post('/ai/smart-continue', {
      content,
      context,
    })
    return response.data
  },
}

// 小说相关API
export const novelApi = {
  /**
   * 获取小说列表
   */
  getNovels: async (): Promise<ApiResponse<{ novels: any[] }>> => {
    const response = await apiClient.get('/novels')
    return response.data
  },

  /**
   * 创建新小说
   */
  createNovel: async (novelData: {
    title: string
    description?: string
    genre?: string
    styleSample?: string
  }): Promise<ApiResponse<{ novel: any }>> => {
    const response = await apiClient.post('/novels', novelData)
    return response.data
  },

  /**
   * 获取小说详情
   */
  getNovel: async (id: string): Promise<ApiResponse<{ novel: any }>> => {
    const response = await apiClient.get(`/novels/${id}`)
    return response.data
  },

  /**
   * 更新小说信息
   */
  updateNovel: async (
    id: string,
    novelData: {
      title?: string
      description?: string
      genre?: string
      styleSample?: string
      status?: string
    }
  ): Promise<ApiResponse<{ novel: any }>> => {
    const response = await apiClient.put(`/novels/${id}`, novelData)
    return response.data
  },

  /**
   * 删除小说
   */
  deleteNovel: async (id: string): Promise<ApiResponse<{ message: string }>> => {
    const response = await apiClient.delete(`/novels/${id}`)
    return response.data
  },

  /**
   * 获取小说的章节列表
   */
  getChapters: async (novelId: string): Promise<ApiResponse<any[]>> => {
    const response = await apiClient.get(`/novels/${novelId}/chapters`)
    return response.data
  },
}

// 错误处理辅助函数
export const handleApiError = (error: any): string => {
  if (error.response?.data?.error?.message) {
    return error.response.data.error.message
  }
  
  if (error.message) {
    return error.message
  }
  
  return '发生未知错误，请稍后重试'
}

// API响应类型守卫
export const isApiSuccess = <T>(response: ApiResponse<T>): response is ApiResponse<T> & { success: true } => {
  return response.success === true
}

export const isApiError = <T>(response: ApiResponse<T>): response is ApiResponse<T> & { success: false } => {
  return response.success === false
}

// 章节相关 API
export const chapterApi = {
  /**
   * 获取章节详情
   */
  getById: async (id: string): Promise<ApiResponse<any>> => {
    const response = await apiClient.get(`/chapters/${id}`)
    return response.data
  },

  /**
   * 创建章节
   */
  create: async (novelId: string, data: {
    title: string
    content?: string
    orderIndex?: number
  }): Promise<ApiResponse<any>> => {
    const response = await apiClient.post(`/novels/${novelId}/chapters`, data)
    return response.data
  },

  /**
   * 更新章节
   */
  update: async (id: string, data: {
    title?: string
    content?: string
    status?: string
  }): Promise<ApiResponse<any>> => {
    const response = await apiClient.put(`/chapters/${id}`, data)
    return response.data
  },

  /**
   * 更新章节内容
   */
  updateContent: async (id: string, content: string): Promise<ApiResponse<any>> => {
    const response = await apiClient.put(`/chapters/${id}/content`, { content })
    return response.data
  },

  /**
   * 删除章节
   */
  delete: async (id: string): Promise<ApiResponse<any>> => {
    const response = await apiClient.delete(`/chapters/${id}`)
    return response.data
  },
}

// 角色 API
export const characterApi = {
  /**
   * 获取小说的所有角色
   */
  getByNovel: async (novelId: string): Promise<ApiResponse<any[]>> => {
    const response = await apiClient.get(`/novels/${novelId}/characters`)
    return response.data
  },

  /**
   * 获取单个角色
   */
  get: async (id: string): Promise<ApiResponse<any>> => {
    const response = await apiClient.get(`/characters/${id}`)
    return response.data
  },

  /**
   * 创建角色
   */
  create: async (data: any): Promise<ApiResponse<any>> => {
    const response = await apiClient.post('/characters', data)
    return response.data
  },

  /**
   * 更新角色
   */
  update: async (id: string, data: any): Promise<ApiResponse<any>> => {
    const response = await apiClient.put(`/characters/${id}`, data)
    return response.data
  },

  /**
   * 删除角色
   */
  delete: async (id: string): Promise<ApiResponse<any>> => {
    const response = await apiClient.delete(`/characters/${id}`)
    return response.data
  },
}

// 世界设定 API
export const worldSettingApi = {
  /**
   * 获取小说的所有世界设定
   */
  getByNovel: async (novelId: string): Promise<ApiResponse<any[]>> => {
    const response = await apiClient.get(`/novels/${novelId}/world-settings`)
    return response.data
  },

  /**
   * 获取单个世界设定
   */
  get: async (id: string): Promise<ApiResponse<any>> => {
    const response = await apiClient.get(`/world-settings/${id}`)
    return response.data
  },

  /**
   * 创建世界设定
   */
  create: async (data: any): Promise<ApiResponse<any>> => {
    const response = await apiClient.post('/world-settings', data)
    return response.data
  },

  /**
   * 更新世界设定
   */
  update: async (id: string, data: any): Promise<ApiResponse<any>> => {
    const response = await apiClient.put(`/world-settings/${id}`, data)
    return response.data
  },

  /**
   * 删除世界设定
   */
  delete: async (id: string): Promise<ApiResponse<any>> => {
    const response = await apiClient.delete(`/world-settings/${id}`)
    return response.data
  },
}

// 大纲 API
export const outlineApi = {
  /**
   * 获取小说的所有大纲
   */
  getByNovel: async (novelId: string): Promise<ApiResponse<any[]>> => {
    const response = await apiClient.get(`/novels/${novelId}/outlines`)
    return response.data
  },

  /**
   * 获取单个大纲
   */
  get: async (id: string): Promise<ApiResponse<any>> => {
    const response = await apiClient.get(`/outlines/${id}`)
    return response.data
  },

  /**
   * 创建大纲
   */
  create: async (data: any): Promise<ApiResponse<any>> => {
    const response = await apiClient.post('/outlines', data)
    return response.data
  },

  /**
   * 更新大纲
   */
  update: async (id: string, data: any): Promise<ApiResponse<any>> => {
    const response = await apiClient.put(`/outlines/${id}`, data)
    return response.data
  },

  /**
   * 删除大纲
   */
  delete: async (id: string): Promise<ApiResponse<any>> => {
    const response = await apiClient.delete(`/outlines/${id}`)
    return response.data
  },
}
