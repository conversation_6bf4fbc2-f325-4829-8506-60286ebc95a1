// AI 服务管理器
import { apiClient } from './api'

export interface AIWritingContext {
  novelId: string
  chapterId?: string
  previousContent: string
  characters: any[]
  worldSettings: any[]
  outline?: any
  styleGuide?: string
}

export interface AIContinueRequest {
  context: AIWritingContext
  prompt?: string
  length?: 'short' | 'medium' | 'long'
  style?: 'narrative' | 'dialogue' | 'description'
  aiConfig?: {
    providerId: string | null
    modelId: string | null
    temperature: number
    maxTokens: number
  }
}

export interface AIContinueResponse {
  suggestions: string[]
  confidence: number
  reasoning: string
}

export interface AIAnalysisRequest {
  content: string
  type: 'style' | 'character' | 'plot' | 'pacing' | 'consistency'
  context?: AIWritingContext
}

export interface AIAnalysisResponse {
  analysis: string
  suggestions: string[]
  score: number
  issues: Array<{
    type: string
    description: string
    severity: 'low' | 'medium' | 'high'
    suggestion: string
  }>
}

export interface AISummaryRequest {
  content: string
  type: 'chapter' | 'plot_points' | 'characters' | 'world_building'
  maxLength?: number
}

export interface AISummaryResponse {
  summary: string
  keyPoints: string[]
  characters: string[]
  plotThreads: Array<{
    thread: string
    status: 'resolved' | 'ongoing' | 'introduced'
  }>
}

export interface AIOutlineRequest {
  title: string
  description: string
  genre: string
  targetLength: number
  existingContent?: string
  styleGuide?: string
}

export interface AIOutlineResponse {
  outline: {
    acts: Array<{
      title: string
      description: string
      chapters: Array<{
        title: string
        description: string
        keyEvents: string[]
        estimatedWordCount: number
      }>
    }>
  }
  reasoning: string
}

export interface AIImageRequest {
  prompt: string
  type: 'scene' | 'character' | 'cover' | 'concept'
  style?: 'realistic' | 'anime' | 'fantasy' | 'sci-fi' | 'watercolor'
  aspectRatio?: '1:1' | '16:9' | '9:16' | '4:3'
}

export interface AIImageResponse {
  imageUrl: string
  prompt: string
  style: string
  metadata: {
    model: string
    seed: number
    steps: number
  }
}

export class AIService {
  /**
   * AI 续写功能
   */
  static async continueWriting(request: AIContinueRequest): Promise<AIContinueResponse> {
    const response = await apiClient.post('/ai/continue', request)
    return response.data
  }

  /**
   * AI 内容分析
   */
  static async analyzeContent(request: AIAnalysisRequest): Promise<AIAnalysisResponse> {
    const response = await apiClient.post('/ai/analyze', request)
    return response.data
  }

  /**
   * AI 内容总结
   */
  static async summarizeContent(request: AISummaryRequest): Promise<AISummaryResponse> {
    const response = await apiClient.post('/ai/summarize', request)
    return response.data
  }

  /**
   * AI 大纲生成
   */
  static async generateOutline(request: AIOutlineRequest): Promise<AIOutlineResponse> {
    const response = await apiClient.post('/ai/outline', request)
    return response.data
  }

  /**
   * AI 图像生成
   */
  static async generateImage(request: AIImageRequest): Promise<AIImageResponse> {
    const response = await apiClient.post('/ai/image', request)
    return response.data
  }

  /**
   * 获取可用的 AI 模型
   */
  static async getAvailableModels(): Promise<{
    text: string[]
    image: string[]
  }> {
    const response = await apiClient.get('/ai/models')
    return response.data
  }

  /**
   * 风格学习 - 分析样本文本并提取风格特征
   */
  static async learnStyle(samples: string[]): Promise<{
    styleGuide: string
    characteristics: string[]
    confidence: number
  }> {
    const response = await apiClient.post('/ai/learn-style', { samples })
    return response.data
  }

  /**
   * 实时写作建议
   */
  static async getWritingSuggestions(
    content: string, 
    context: AIWritingContext
  ): Promise<{
    suggestions: Array<{
      type: 'grammar' | 'style' | 'plot' | 'character'
      message: string
      suggestion: string
      confidence: number
    }>
  }> {
    const response = await apiClient.post('/ai/suggestions', { content, context })
    return response.data
  }

  /**
   * 情节线索跟踪
   */
  static async trackPlotThreads(
    novelId: string
  ): Promise<{
    threads: Array<{
      id: string
      description: string
      status: 'introduced' | 'developing' | 'resolved' | 'abandoned'
      chapters: string[]
      importance: 'high' | 'medium' | 'low'
    }>
    suggestions: string[]
  }> {
    const response = await apiClient.get(`/ai/plot-threads/${novelId}`)
    return response.data
  }

  /**
   * 智能前文总结
   */
  static async summarizePreviousContent(
    novelId: string,
    chapterId: string,
    fullContent: string
  ): Promise<{
    summary: string
    keyCharacters: Array<{
      name: string
      role: string
      currentState: string
    }>
    plotPoints: string[]
    worldState: string[]
    unresolvedThreads: string[]
    recentEvents: string[]
  }> {
    const response = await apiClient.post('/ai/summarize-previous', {
      novelId,
      chapterId,
      fullContent
    })
    return response.data.data
  }
}

export default AIService
