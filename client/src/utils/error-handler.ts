import toast from 'react-hot-toast'

/**
 * API 错误响应接口
 */
export interface APIError {
  code: string
  message: string
  details?: {
    provider?: string
    model?: string
    reason?: string
    timeout?: string
    quotaType?: string
    resetTime?: string
    suggestion?: string
    originalError?: string
    [key: string]: any
  }
}

/**
 * 错误处理选项
 */
export interface ErrorHandlerOptions {
  showToast?: boolean
  toastDuration?: number
  showDetails?: boolean
  detailsDelay?: number
  fallbackMessage?: string
}

/**
 * 默认错误处理选项
 */
const defaultOptions: ErrorHandlerOptions = {
  showToast: true,
  toastDuration: 5000,
  showDetails: true,
  detailsDelay: 500,
  fallbackMessage: '操作失败，请重试'
}

/**
 * 处理 AI 相关错误
 */
export function handleAIError(error: any, options: ErrorHandlerOptions = {}) {
  const opts = { ...defaultOptions, ...options }
  
  console.error('AI Error:', error)
  
  let errorMessage = opts.fallbackMessage!
  let errorDetails = ''
  
  if (error?.response?.data?.error) {
    const apiError: APIError = error.response.data.error
    errorMessage = apiError.message || errorMessage
    
    // 根据错误类型提供更具体的提示
    if (apiError.details && opts.showDetails) {
      const details = apiError.details
      
      switch (apiError.code) {
        case 'AI_PROVIDER_UNAVAILABLE':
          errorDetails = `AI 服务提供商 "${details.provider}" 暂时不可用`
          if (details.reason) {
            errorDetails += `：${details.reason}`
          }
          break
          
        case 'AI_MODEL_UNAVAILABLE':
          errorDetails = `AI 模型 "${details.model}" 暂时不可用`
          if (details.reason?.includes('rate limit')) {
            errorDetails += '，已达到使用限制'
          }
          break
          
        case 'AI_REQUEST_TIMEOUT':
          errorDetails = `请求超时（${details.timeout || '30秒'}），请尝试缩短内容长度`
          break
          
        case 'AI_QUOTA_EXCEEDED':
          errorDetails = `已达到${details.quotaType || '每日'}使用限制`
          if (details.resetTime) {
            const resetDate = new Date(details.resetTime)
            errorDetails += `，将在 ${resetDate.toLocaleString()} 重置`
          }
          break
          
        case 'AI_CONTENT_FILTERED':
          errorDetails = '内容被过滤，请修改提示词或前文内容'
          break
          
        case 'AI_NETWORK_ERROR':
          errorDetails = '网络连接失败，请检查网络连接'
          break
          
        case 'AI_INVALID_PARAMETERS':
          errorDetails = '请求参数有误，请检查输入内容'
          break
          
        default:
          if (details.suggestion) {
            errorDetails = details.suggestion
          }
      }
    }
  } else if (error?.message) {
    errorMessage = error.message
  }
  
  // 显示主要错误信息
  if (opts.showToast) {
    toast.error(errorMessage, {
      duration: opts.toastDuration,
    })
    
    // 如果有详细信息，显示额外的提示
    if (errorDetails && opts.showDetails) {
      setTimeout(() => {
        toast(errorDetails, {
          icon: '💡',
          duration: opts.toastDuration! + 3000,
        })
      }, opts.detailsDelay)
    }
  }
  
  return {
    message: errorMessage,
    details: errorDetails,
    code: error?.response?.data?.error?.code || 'UNKNOWN_ERROR'
  }
}

/**
 * 处理通用 API 错误
 */
export function handleAPIError(error: any, options: ErrorHandlerOptions = {}) {
  const opts = { ...defaultOptions, ...options }
  
  console.error('API Error:', error)
  
  let errorMessage = opts.fallbackMessage!
  
  if (error?.response?.data?.error) {
    const apiError: APIError = error.response.data.error
    errorMessage = apiError.message || errorMessage
  } else if (error?.response?.status) {
    switch (error.response.status) {
      case 400:
        errorMessage = '请求参数错误'
        break
      case 401:
        errorMessage = '未授权，请重新登录'
        break
      case 403:
        errorMessage = '权限不足'
        break
      case 404:
        errorMessage = '资源未找到'
        break
      case 429:
        errorMessage = '请求过于频繁，请稍后重试'
        break
      case 500:
        errorMessage = '服务器内部错误'
        break
      case 502:
        errorMessage = '网关错误'
        break
      case 503:
        errorMessage = '服务暂时不可用'
        break
      default:
        errorMessage = `请求失败 (${error.response.status})`
    }
  } else if (error?.message) {
    errorMessage = error.message
  }
  
  if (opts.showToast) {
    toast.error(errorMessage, {
      duration: opts.toastDuration,
    })
  }
  
  return {
    message: errorMessage,
    code: error?.response?.data?.error?.code || 'UNKNOWN_ERROR'
  }
}

/**
 * 处理网络错误
 */
export function handleNetworkError(error: any, options: ErrorHandlerOptions = {}) {
  const opts = { ...defaultOptions, ...options }
  
  console.error('Network Error:', error)
  
  let errorMessage = '网络连接失败，请检查网络连接'
  
  if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Network Error')) {
    errorMessage = '网络连接失败，请检查网络连接'
  } else if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
    errorMessage = '请求超时，请重试'
  }
  
  if (opts.showToast) {
    toast.error(errorMessage, {
      duration: opts.toastDuration,
    })
  }
  
  return {
    message: errorMessage,
    code: 'NETWORK_ERROR'
  }
}
