import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Login } from './Login'

// Mock hooks
const mockLoginMutation = {
  mutate: vi.fn(),
  isLoading: false,
  error: null,
}

const mockUseLoginRedirect = {
  isAuthenticated: false,
}

vi.mock('../hooks/useAuth', () => ({
  useAuthMutations: () => ({
    loginMutation: mockLoginMutation,
  }),
  useLoginRedirect: () => mockUseLoginRedirect,
  useAuthValidation: () => ({
    validateEmail: (email: string) => {
      if (!email) return '邮箱不能为空'
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) return '邮箱格式不正确'
      return null
    },
    validatePassword: (password: string) => {
      if (!password) return '密码不能为空'
      if (password.length < 8) return '密码长度至少8位'
      return null
    },
  }),
}))

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </BrowserRouter>
  )
}

describe('Login页面', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockLoginMutation.isLoading = false
    mockLoginMutation.error = null
    mockUseLoginRedirect.isAuthenticated = false
  })

  describe('页面渲染', () => {
    it('应该渲染登录表单', () => {
      render(<Login />, { wrapper: createWrapper() })
      
      expect(screen.getByText('登录到 AugmentWriter')).toBeInTheDocument()
      expect(screen.getByLabelText('邮箱')).toBeInTheDocument()
      expect(screen.getByLabelText('密码')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '登录' })).toBeInTheDocument()
    })

    it('应该显示注册链接', () => {
      render(<Login />, { wrapper: createWrapper() })
      
      expect(screen.getByText('还没有账户？')).toBeInTheDocument()
      expect(screen.getByRole('link', { name: '立即注册' })).toBeInTheDocument()
    })

    it('应该显示忘记密码链接', () => {
      render(<Login />, { wrapper: createWrapper() })
      
      expect(screen.getByRole('link', { name: '忘记密码？' })).toBeInTheDocument()
    })
  })

  describe('表单验证', () => {
    it('应该验证邮箱格式', async () => {
      render(<Login />, { wrapper: createWrapper() })
      
      const emailInput = screen.getByLabelText('邮箱')
      const submitButton = screen.getByRole('button', { name: '登录' })
      
      // 输入无效邮箱
      fireEvent.change(emailInput, { target: { value: 'invalid-email' } })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText('邮箱格式不正确')).toBeInTheDocument()
      })
    })

    it('应该验证密码长度', async () => {
      render(<Login />, { wrapper: createWrapper() })
      
      const passwordInput = screen.getByLabelText('密码')
      const submitButton = screen.getByRole('button', { name: '登录' })
      
      // 输入过短密码
      fireEvent.change(passwordInput, { target: { value: '123' } })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText('密码长度至少8位')).toBeInTheDocument()
      })
    })

    it('应该验证必填字段', async () => {
      render(<Login />, { wrapper: createWrapper() })
      
      const submitButton = screen.getByRole('button', { name: '登录' })
      
      // 不输入任何内容直接提交
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText('邮箱不能为空')).toBeInTheDocument()
        expect(screen.getByText('密码不能为空')).toBeInTheDocument()
      })
    })

    it('应该在输入有效数据后清除错误', async () => {
      render(<Login />, { wrapper: createWrapper() })
      
      const emailInput = screen.getByLabelText('邮箱')
      const submitButton = screen.getByRole('button', { name: '登录' })
      
      // 先触发错误
      fireEvent.click(submitButton)
      await waitFor(() => {
        expect(screen.getByText('邮箱不能为空')).toBeInTheDocument()
      })
      
      // 输入有效邮箱
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      
      await waitFor(() => {
        expect(screen.queryByText('邮箱不能为空')).not.toBeInTheDocument()
      })
    })
  })

  describe('表单提交', () => {
    it('应该提交有效的登录数据', async () => {
      render(<Login />, { wrapper: createWrapper() })
      
      const emailInput = screen.getByLabelText('邮箱')
      const passwordInput = screen.getByLabelText('密码')
      const submitButton = screen.getByRole('button', { name: '登录' })
      
      // 输入有效数据
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      
      // 提交表单
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(mockLoginMutation.mutate).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        })
      })
    })

    it('应该在加载时禁用提交按钮', () => {
      mockLoginMutation.isLoading = true
      
      render(<Login />, { wrapper: createWrapper() })
      
      const submitButton = screen.getByRole('button', { name: '登录中...' })
      expect(submitButton).toBeDisabled()
    })

    it('应该在加载时显示加载状态', () => {
      mockLoginMutation.isLoading = true
      
      render(<Login />, { wrapper: createWrapper() })
      
      expect(screen.getByText('登录中...')).toBeInTheDocument()
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    })
  })

  describe('键盘交互', () => {
    it('应该支持Enter键提交', async () => {
      render(<Login />, { wrapper: createWrapper() })
      
      const emailInput = screen.getByLabelText('邮箱')
      const passwordInput = screen.getByLabelText('密码')
      
      // 输入有效数据
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      
      // 在密码框按Enter
      fireEvent.keyDown(passwordInput, { key: 'Enter', code: 'Enter' })
      
      await waitFor(() => {
        expect(mockLoginMutation.mutate).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        })
      })
    })
  })

  describe('密码可见性切换', () => {
    it('应该切换密码可见性', () => {
      render(<Login />, { wrapper: createWrapper() })
      
      const passwordInput = screen.getByLabelText('密码') as HTMLInputElement
      const toggleButton = screen.getByRole('button', { name: '显示密码' })
      
      // 初始状态应该是password类型
      expect(passwordInput.type).toBe('password')
      
      // 点击切换按钮
      fireEvent.click(toggleButton)
      expect(passwordInput.type).toBe('text')
      
      // 再次点击切换回来
      fireEvent.click(toggleButton)
      expect(passwordInput.type).toBe('password')
    })

    it('应该更新切换按钮的aria-label', () => {
      render(<Login />, { wrapper: createWrapper() })
      
      const toggleButton = screen.getByRole('button', { name: '显示密码' })
      
      // 点击显示密码
      fireEvent.click(toggleButton)
      expect(toggleButton).toHaveAttribute('aria-label', '隐藏密码')
      
      // 点击隐藏密码
      fireEvent.click(toggleButton)
      expect(toggleButton).toHaveAttribute('aria-label', '显示密码')
    })
  })

  describe('可访问性', () => {
    it('应该有正确的页面标题', () => {
      render(<Login />, { wrapper: createWrapper() })
      
      const heading = screen.getByRole('heading', { level: 1 })
      expect(heading).toHaveTextContent('登录到 AugmentWriter')
    })

    it('应该有正确的表单标签', () => {
      render(<Login />, { wrapper: createWrapper() })
      
      const emailInput = screen.getByLabelText('邮箱')
      const passwordInput = screen.getByLabelText('密码')
      
      expect(emailInput).toHaveAttribute('type', 'email')
      expect(passwordInput).toHaveAttribute('type', 'password')
      expect(emailInput).toHaveAttribute('required')
      expect(passwordInput).toHaveAttribute('required')
    })

    it('应该有正确的错误提示关联', async () => {
      render(<Login />, { wrapper: createWrapper() })
      
      const submitButton = screen.getByRole('button', { name: '登录' })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        const emailInput = screen.getByLabelText('邮箱')
        const errorMessage = screen.getByText('邮箱不能为空')
        
        expect(emailInput).toHaveAttribute('aria-invalid', 'true')
        expect(emailInput).toHaveAttribute('aria-describedby')
        expect(errorMessage).toHaveAttribute('role', 'alert')
      })
    })
  })
})
