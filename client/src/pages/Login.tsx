import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Eye, EyeOff, Mail, Lock } from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Input } from '../components/ui/Input'
import { useAuthMutations, useLoginRedirect, useAuthValidation } from '../hooks/useAuth'

interface FormData {
  email: string
  password: string
}

interface FormErrors {
  email?: string
  password?: string
}

export function Login() {
  // 状态管理
  const [formData, setFormData] = useState<FormData>({
    email: '',
    password: '',
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [showPassword, setShowPassword] = useState(false)

  // Hooks
  const { loginMutation } = useAuthMutations()
  const { isAuthenticated } = useLoginRedirect()
  const { validateEmail, validatePassword } = useAuthValidation()

  // 如果已登录，不渲染登录页面
  if (isAuthenticated) {
    return null
  }

  // 处理输入变化
  const handleInputChange = (field: keyof FormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}
    
    const emailError = validateEmail(formData.email)
    if (emailError) {
      newErrors.email = emailError
    }
    
    const passwordError = validatePassword(formData.password)
    if (passwordError) {
      newErrors.password = passwordError
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (validateForm()) {
      loginMutation.mutate(formData)
    }
  }

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit(e as any)
    }
  }

  // 切换密码可见性
  const togglePasswordVisibility = () => {
    setShowPassword(prev => !prev)
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* 头部 */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">
            登录到 AugmentWriter
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            开始您的AI辅助创作之旅
          </p>
        </div>

        {/* 登录表单 */}
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* 邮箱输入 */}
            <Input
              label="邮箱"
              type="email"
              value={formData.email}
              onChange={handleInputChange('email')}
              onKeyDown={handleKeyDown}
              error={errors.email}
              leftIcon={<Mail className="w-4 h-4" />}
              placeholder="请输入您的邮箱"
              required
              autoComplete="email"
            />

            {/* 密码输入 */}
            <Input
              label="密码"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleInputChange('password')}
              onKeyDown={handleKeyDown}
              error={errors.password}
              leftIcon={<Lock className="w-4 h-4" />}
              rightIcon={
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="text-gray-400 hover:text-gray-600 focus:outline-none"
                  aria-label={showPassword ? '隐藏密码' : '显示密码'}
                >
                  {showPassword ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </button>
              }
              placeholder="请输入您的密码"
              required
              autoComplete="current-password"
            />
          </div>

          {/* 忘记密码链接 */}
          <div className="flex justify-end">
            <Link
              to="/forgot-password"
              className="text-sm text-primary-600 hover:text-primary-500 focus:outline-none focus:underline"
            >
              忘记密码？
            </Link>
          </div>

          {/* 提交按钮 */}
          <Button
            type="submit"
            variant="primary"
            size="lg"
            fullWidth
            loading={loginMutation.isPending}
          >
            {loginMutation.isPending ? '登录中...' : '登录'}
          </Button>

          {/* 注册链接 */}
          <div className="text-center">
            <span className="text-sm text-gray-600">
              还没有账户？{' '}
              <Link
                to="/register"
                className="font-medium text-primary-600 hover:text-primary-500 focus:outline-none focus:underline"
              >
                立即注册
              </Link>
            </span>
          </div>
        </form>

        {/* 功能特色 */}
        <div className="mt-8 pt-8 border-t border-gray-200">
          <div className="text-center">
            <h3 className="text-sm font-medium text-gray-900 mb-4">
              为什么选择 AugmentWriter？
            </h3>
            <div className="grid grid-cols-1 gap-3 text-xs text-gray-600">
              <div className="flex items-center justify-center space-x-2">
                <span className="w-2 h-2 bg-primary-500 rounded-full"></span>
                <span>AI智能续写，激发创作灵感</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <span className="w-2 h-2 bg-primary-500 rounded-full"></span>
                <span>自动生成大纲，结构化创作</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <span className="w-2 h-2 bg-primary-500 rounded-full"></span>
                <span>风格分析，保持写作一致性</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Login
