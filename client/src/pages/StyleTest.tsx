
import { Button } from '../components/ui/Button'
import { Input } from '../components/ui/Input'

export default function StyleTest() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">
          🎨 样式测试页面
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* 颜色测试 */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">颜色系统</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 bg-primary-500 rounded"></div>
                <span className="text-primary-600 font-medium">Primary Blue</span>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 bg-secondary-500 rounded"></div>
                <span className="text-secondary-600 font-medium">Secondary Gray</span>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 bg-accent-500 rounded"></div>
                <span className="text-accent-600 font-medium">Accent Purple</span>
              </div>
            </div>
          </div>

          {/* 按钮测试 */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">按钮组件</h2>
            <div className="space-y-4">
              <Button variant="primary" size="lg">
                主要按钮
              </Button>
              <Button variant="secondary" size="md">
                次要按钮
              </Button>
              <Button variant="outline" size="sm">
                轮廓按钮
              </Button>
              <Button variant="ghost" disabled>
                禁用按钮
              </Button>
            </div>
          </div>

          {/* 输入框测试 */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">输入组件</h2>
            <div className="space-y-4">
              <Input
                label="用户名"
                placeholder="请输入用户名"
                helperText="用户名必须是唯一的"
              />
              <Input
                label="密码"
                type="password"
                placeholder="请输入密码"
                error="密码长度至少8位"
              />
              <Input
                label="邮箱"
                type="email"
                placeholder="请输入邮箱"
                required
              />
            </div>
          </div>

          {/* 卡片测试 */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">卡片样式</h2>
            <div className="space-y-4">
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-lg">
                <h3 className="font-semibold">渐变卡片</h3>
                <p className="text-blue-100">这是一个渐变背景的卡片</p>
              </div>
              <div className="border-2 border-dashed border-gray-300 p-4 rounded-lg">
                <h3 className="font-semibold text-gray-700">虚线边框</h3>
                <p className="text-gray-600">这是一个虚线边框的卡片</p>
              </div>
            </div>
          </div>
        </div>

        {/* 响应式测试 */}
        <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">响应式网格</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((num) => (
              <div
                key={num}
                className="bg-gradient-to-br from-indigo-400 to-purple-500 text-white p-4 rounded-lg text-center font-semibold"
              >
                网格 {num}
              </div>
            ))}
          </div>
        </div>

        {/* 动画测试 */}
        <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">动画效果</h2>
          <div className="flex flex-wrap gap-4">
            <div className="w-16 h-16 bg-blue-500 rounded-lg animate-pulse"></div>
            <div className="w-16 h-16 bg-green-500 rounded-lg animate-bounce"></div>
            <div className="w-16 h-16 bg-red-500 rounded-lg animate-spin"></div>
            <div className="w-16 h-16 bg-yellow-500 rounded-lg hover:scale-110 transition-transform duration-300 cursor-pointer"></div>
          </div>
        </div>

        {/* 返回按钮 */}
        <div className="mt-8 text-center">
          <Button
            variant="outline"
            onClick={() => window.history.back()}
          >
            ← 返回
          </Button>
        </div>
      </div>
    </div>
  )
}
