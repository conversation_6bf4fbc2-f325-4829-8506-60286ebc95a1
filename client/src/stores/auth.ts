import { create } from 'zustand'
import type { User } from '@shared/index.js'

interface AuthState {
  // 状态
  user: Omit<User, 'password'> | null
  accessToken: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean

  // 操作
  login: (user: Omit<User, 'password'>, accessToken: string, refreshToken: string) => void
  logout: () => void
  updateUser: (user: Omit<User, 'password'>) => void
  updateTokens: (accessToken: string, refreshToken: string) => void
  setLoading: (loading: boolean) => void
  initializeAuth: () => void
}

// localStorage键名
const STORAGE_KEYS = {
  USER: 'auth_user',
  ACCESS_TOKEN: 'auth_access_token',
  REFRESH_TOKEN: 'auth_refresh_token',
} as const

// 从localStorage获取数据的辅助函数
const getStorageItem = (key: string): string | null => {
  try {
    return localStorage.getItem(key)
  } catch {
    return null
  }
}

// 设置localStorage数据的辅助函数
const setStorageItem = (key: string, value: string): void => {
  try {
    localStorage.setItem(key, value)
  } catch {
    // 忽略存储错误
  }
}

// 删除localStorage数据的辅助函数
const removeStorageItem = (key: string): void => {
  try {
    localStorage.removeItem(key)
  } catch {
    // 忽略删除错误
  }
}

export const useAuthStore = create<AuthState>((set, get) => ({
  // 初始状态
  user: null,
  accessToken: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,

  // 登录
  login: (user, accessToken, refreshToken) => {
    // 保存到localStorage
    setStorageItem(STORAGE_KEYS.USER, JSON.stringify(user))
    setStorageItem(STORAGE_KEYS.ACCESS_TOKEN, accessToken)
    setStorageItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken)

    // 更新状态
    set({
      user,
      accessToken,
      refreshToken,
      isAuthenticated: true,
      isLoading: false,
    })
  },

  // 登出
  logout: () => {
    // 清除localStorage
    removeStorageItem(STORAGE_KEYS.USER)
    removeStorageItem(STORAGE_KEYS.ACCESS_TOKEN)
    removeStorageItem(STORAGE_KEYS.REFRESH_TOKEN)

    // 重置状态
    set({
      user: null,
      accessToken: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
    })
  },

  // 更新用户信息
  updateUser: (user) => {
    const currentState = get()
    if (!currentState.isAuthenticated) {
      return
    }

    // 更新localStorage
    setStorageItem(STORAGE_KEYS.USER, JSON.stringify(user))

    // 更新状态
    set({ user })
  },

  // 更新令牌
  updateTokens: (accessToken, refreshToken) => {
    // 更新localStorage
    setStorageItem(STORAGE_KEYS.ACCESS_TOKEN, accessToken)
    setStorageItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken)

    // 更新状态
    set({ accessToken, refreshToken })
  },

  // 设置加载状态
  setLoading: (isLoading) => {
    set({ isLoading })
  },

  // 初始化认证状态（从localStorage恢复）
  initializeAuth: () => {
    try {
      const userStr = getStorageItem(STORAGE_KEYS.USER)
      const accessToken = getStorageItem(STORAGE_KEYS.ACCESS_TOKEN)
      const refreshToken = getStorageItem(STORAGE_KEYS.REFRESH_TOKEN)

      if (userStr && accessToken && refreshToken) {
        const user = JSON.parse(userStr)
        set({
          user,
          accessToken,
          refreshToken,
          isAuthenticated: true,
          isLoading: false,
        })
      } else {
        // 如果数据不完整，清除所有认证信息
        get().logout()
      }
    } catch (error) {
      console.error('初始化认证状态失败:', error)
      get().logout()
    }
  },
}))

// 选择器函数，用于优化性能
export const authSelectors = {
  user: (state: AuthState) => state.user,
  isAuthenticated: (state: AuthState) => state.isAuthenticated,
  isLoading: (state: AuthState) => state.isLoading,
  accessToken: (state: AuthState) => state.accessToken,
  refreshToken: (state: AuthState) => state.refreshToken,
}

// Hook用于获取认证状态
export const useAuth = () => {
  const store = useAuthStore()
  return {
    user: store.user,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    login: store.login,
    logout: store.logout,
    updateUser: store.updateUser,
  }
}

// Hook用于获取令牌
export const useAuthTokens = () => {
  const accessToken = useAuthStore(authSelectors.accessToken)
  const refreshToken = useAuthStore(authSelectors.refreshToken)
  const updateTokens = useAuthStore((state) => state.updateTokens)
  
  return {
    accessToken,
    refreshToken,
    updateTokens,
  }
}
