import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// AI 配置状态接口
interface AIConfigState {
  // 当前选择的 Provider 和模型
  selectedProviderId: string | null
  selectedModelId: string | null
  
  // 通用设置
  temperature: number
  maxTokens: number
  autoSave: boolean
  realTimeSuggestions: boolean
  
  // 操作方法
  setSelectedProvider: (providerId: string) => void
  setSelectedModel: (modelId: string) => void
  setTemperature: (temperature: number) => void
  setMaxTokens: (maxTokens: number) => void
  setAutoSave: (autoSave: boolean) => void
  setRealTimeSuggestions: (realTimeSuggestions: boolean) => void
  
  // 获取当前配置
  getCurrentConfig: () => {
    providerId: string | null
    modelId: string | null
    temperature: number
    maxTokens: number
  }
}

// 创建 AI 配置 store
export const useAIConfigStore = create<AIConfigState>()(
  persist(
    (set, get) => ({
      // 初始状态
      selectedProviderId: null, // 将使用默认 Provider
      selectedModelId: null, // 将使用 Provider 的第一个模型
      temperature: 0.7,
      maxTokens: 1000,
      autoSave: true,
      realTimeSuggestions: false,
      
      // 设置方法
      setSelectedProvider: (providerId: string) => {
        set({ 
          selectedProviderId: providerId,
          selectedModelId: null // 重置模型选择
        })
      },
      
      setSelectedModel: (modelId: string) => {
        set({ selectedModelId: modelId })
      },
      
      setTemperature: (temperature: number) => {
        set({ temperature })
      },
      
      setMaxTokens: (maxTokens: number) => {
        set({ maxTokens })
      },
      
      setAutoSave: (autoSave: boolean) => {
        set({ autoSave })
      },
      
      setRealTimeSuggestions: (realTimeSuggestions: boolean) => {
        set({ realTimeSuggestions })
      },
      
      // 获取当前配置
      getCurrentConfig: () => {
        const state = get()
        return {
          providerId: state.selectedProviderId,
          modelId: state.selectedModelId,
          temperature: state.temperature,
          maxTokens: state.maxTokens
        }
      }
    }),
    {
      name: 'ai-config-storage', // 本地存储的 key
      partialize: (state) => ({
        selectedProviderId: state.selectedProviderId,
        selectedModelId: state.selectedModelId,
        temperature: state.temperature,
        maxTokens: state.maxTokens,
        autoSave: state.autoSave,
        realTimeSuggestions: state.realTimeSuggestions
      })
    }
  )
)

export default useAIConfigStore
