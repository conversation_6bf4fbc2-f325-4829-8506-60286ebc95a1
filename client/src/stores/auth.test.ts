import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useAuthStore } from './auth'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

describe('AuthStore', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // 重置store状态
    useAuthStore.getState().logout()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      const { result } = renderHook(() => useAuthStore())
      
      expect(result.current.user).toBeNull()
      expect(result.current.accessToken).toBeNull()
      expect(result.current.refreshToken).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.isLoading).toBe(false)
    })

    it('应该从localStorage恢复认证状态', () => {
      const mockUser = {
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        avatar: null,
        settings: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      const mockAccessToken = 'access-token-123'
      const mockRefreshToken = 'refresh-token-123'

      localStorageMock.getItem.mockImplementation((key) => {
        switch (key) {
          case 'auth_user':
            return JSON.stringify(mockUser)
          case 'auth_access_token':
            return mockAccessToken
          case 'auth_refresh_token':
            return mockRefreshToken
          default:
            return null
        }
      })

      const { result } = renderHook(() => useAuthStore())
      
      act(() => {
        result.current.initializeAuth()
      })

      expect(result.current.user).toEqual(mockUser)
      expect(result.current.accessToken).toBe(mockAccessToken)
      expect(result.current.refreshToken).toBe(mockRefreshToken)
      expect(result.current.isAuthenticated).toBe(true)
    })
  })

  describe('登录功能', () => {
    it('应该正确设置登录状态', () => {
      const { result } = renderHook(() => useAuthStore())
      
      const mockUser = {
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        avatar: null,
        settings: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      const mockAccessToken = 'access-token-123'
      const mockRefreshToken = 'refresh-token-123'

      act(() => {
        result.current.login(mockUser, mockAccessToken, mockRefreshToken)
      })

      expect(result.current.user).toEqual(mockUser)
      expect(result.current.accessToken).toBe(mockAccessToken)
      expect(result.current.refreshToken).toBe(mockRefreshToken)
      expect(result.current.isAuthenticated).toBe(true)

      // 验证localStorage调用
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_user', JSON.stringify(mockUser))
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_access_token', mockAccessToken)
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_refresh_token', mockRefreshToken)
    })
  })

  describe('登出功能', () => {
    it('应该清除所有认证状态', () => {
      const { result } = renderHook(() => useAuthStore())
      
      // 先设置登录状态
      const mockUser = {
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        avatar: null,
        settings: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      
      act(() => {
        result.current.login(mockUser, 'access-token', 'refresh-token')
      })

      // 验证登录状态
      expect(result.current.isAuthenticated).toBe(true)

      // 执行登出
      act(() => {
        result.current.logout()
      })

      expect(result.current.user).toBeNull()
      expect(result.current.accessToken).toBeNull()
      expect(result.current.refreshToken).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)

      // 验证localStorage清除
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_user')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_access_token')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_refresh_token')
    })
  })

  describe('更新用户信息', () => {
    it('应该更新用户信息并同步到localStorage', () => {
      const { result } = renderHook(() => useAuthStore())
      
      const initialUser = {
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        avatar: null,
        settings: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      
      // 先登录
      act(() => {
        result.current.login(initialUser, 'access-token', 'refresh-token')
      })

      // 更新用户信息
      const updatedUser = {
        ...initialUser,
        username: 'newusername',
        avatar: 'https://example.com/avatar.jpg',
      }

      act(() => {
        result.current.updateUser(updatedUser)
      })

      expect(result.current.user).toEqual(updatedUser)
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_user', JSON.stringify(updatedUser))
    })

    it('应该在未登录时忽略更新', () => {
      const { result } = renderHook(() => useAuthStore())
      
      const mockUser = {
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        avatar: null,
        settings: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      act(() => {
        result.current.updateUser(mockUser)
      })

      expect(result.current.user).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
    })
  })

  describe('更新令牌', () => {
    it('应该更新访问令牌', () => {
      const { result } = renderHook(() => useAuthStore())
      
      const mockUser = {
        id: 'user-123',
        username: 'testuser',
        email: '<EMAIL>',
        avatar: null,
        settings: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      
      // 先登录
      act(() => {
        result.current.login(mockUser, 'old-access-token', 'refresh-token')
      })

      // 更新令牌
      const newAccessToken = 'new-access-token'
      const newRefreshToken = 'new-refresh-token'

      act(() => {
        result.current.updateTokens(newAccessToken, newRefreshToken)
      })

      expect(result.current.accessToken).toBe(newAccessToken)
      expect(result.current.refreshToken).toBe(newRefreshToken)
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_access_token', newAccessToken)
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_refresh_token', newRefreshToken)
    })
  })

  describe('加载状态', () => {
    it('应该正确管理加载状态', () => {
      const { result } = renderHook(() => useAuthStore())
      
      expect(result.current.isLoading).toBe(false)

      act(() => {
        result.current.setLoading(true)
      })

      expect(result.current.isLoading).toBe(true)

      act(() => {
        result.current.setLoading(false)
      })

      expect(result.current.isLoading).toBe(false)
    })
  })
})
