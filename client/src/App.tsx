import React, { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuthStore } from './stores/auth'
import Login from './pages/Login'
import StyleTest from './pages/StyleTest'
import NovelList from './components/writing/NovelList'
import WritingWorkspace from './components/writing/WritingWorkspace'



// 临时的注册页面
function Register() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          注册功能开发中
        </h1>
        <p className="text-gray-600 mb-6">
          注册页面即将上线，敬请期待！
        </p>
        <a
          href="/login"
          className="btn btn-primary"
        >
          返回登录
        </a>
      </div>
    </div>
  )
}

// 临时的忘记密码页面
function ForgotPassword() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          忘记密码功能开发中
        </h1>
        <p className="text-gray-600 mb-6">
          密码重置功能即将上线，敬请期待！
        </p>
        <a
          href="/login"
          className="btn btn-primary"
        >
          返回登录
        </a>
      </div>
    </div>
  )
}

// 路由保护组件
function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading } = useAuthStore()
  
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="loading-spinner w-8 h-8 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }
  
  return <>{children}</>
}

// 公共路由组件（已登录用户重定向到dashboard）
function PublicRoute({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading } = useAuthStore()
  
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="loading-spinner w-8 h-8 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }
  
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />
  }
  
  return <>{children}</>
}

function App() {
  const { initializeAuth } = useAuthStore()
  
  // 初始化认证状态
  useEffect(() => {
    initializeAuth()
  }, [initializeAuth])
  
  return (
    <div className="App">
      <Routes>
        {/* 公共路由 */}
        <Route
          path="/login"
          element={
            <PublicRoute>
              <Login />
            </PublicRoute>
          }
        />
        <Route
          path="/register"
          element={
            <PublicRoute>
              <Register />
            </PublicRoute>
          }
        />
        <Route
          path="/forgot-password"
          element={
            <PublicRoute>
              <ForgotPassword />
            </PublicRoute>
          }
        />

        {/* 样式测试页面（开发用） */}
        <Route path="/style-test" element={<StyleTest />} />

        {/* 受保护的路由 */}
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <NovelList />
            </ProtectedRoute>
          }
        />

        <Route
          path="/novel/:novelId"
          element={
            <ProtectedRoute>
              <WritingWorkspace />
            </ProtectedRoute>
          }
        />
        
        {/* 默认重定向 */}
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        
        {/* 404页面 */}
        <Route
          path="*"
          element={
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
                <p className="text-gray-600 mb-6">页面未找到</p>
                <a href="/" className="btn btn-primary">
                  返回首页
                </a>
              </div>
            </div>
          }
        />
      </Routes>
    </div>
  )
}

export default App
