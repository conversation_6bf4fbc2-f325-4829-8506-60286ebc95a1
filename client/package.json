{"name": "augment-writer-client", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.3.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.85.9", "@tiptap/extension-character-count": "^2.1.13", "@tiptap/extension-collaboration": "^2.1.13", "@tiptap/extension-collaboration-cursor": "^2.1.13", "@tiptap/extension-color": "^3.3.1", "@tiptap/extension-highlight": "^3.3.1", "@tiptap/extension-placeholder": "^2.26.1", "@tiptap/extension-text-style": "^3.3.1", "@tiptap/react": "^2.26.1", "@tiptap/starter-kit": "^2.26.1", "@types/lodash-es": "^4.17.12", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "lodash-es": "^4.17.21", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.6.0", "react-markdown": "^9.0.1", "react-router-dom": "^6.20.1", "rehype-highlight": "^7.0.0", "remark-gfm": "^4.0.0", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.0.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.10.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^0.34.6"}}