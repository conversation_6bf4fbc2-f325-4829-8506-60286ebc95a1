# 多阶段构建 Dockerfile
FROM node:18-alpine AS base

# 安装 pnpm
RUN npm install -g pnpm

# 设置工作目录
WORKDIR /app

# 复制 package.json 文件
COPY package.json pnpm-lock.yaml ./
COPY client/package.json ./client/
COPY server/package.json ./server/
COPY shared/package.json ./shared/

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建阶段
FROM base AS builder

# 构建共享包
RUN pnpm run build:shared

# 构建前端
RUN pnpm run build:client

# 构建后端
RUN pnpm run build:server

# 生产阶段
FROM node:18-alpine AS production

# 安装 pnpm
RUN npm install -g pnpm

# 创建应用用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S augmentwriter -u 1001

# 设置工作目录
WORKDIR /app

# 复制 package.json 文件
COPY package.json pnpm-lock.yaml ./
COPY server/package.json ./server/
COPY shared/package.json ./shared/

# 只安装生产依赖
RUN pnpm install --frozen-lockfile --prod

# 从构建阶段复制构建产物
COPY --from=builder --chown=augmentwriter:nodejs /app/server/dist ./server/dist
COPY --from=builder --chown=augmentwriter:nodejs /app/shared/dist ./shared/dist
COPY --from=builder --chown=augmentwriter:nodejs /app/client/dist ./client/dist

# 复制 Prisma 文件
COPY --chown=augmentwriter:nodejs server/prisma ./server/prisma

# 切换到应用用户
USER augmentwriter

# 暴露端口
EXPOSE 8989

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node server/dist/health-check.js || exit 1

# 启动命令
CMD ["node", "server/dist/index.js"]
