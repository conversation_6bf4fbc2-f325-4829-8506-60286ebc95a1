# 数据库配置
# 格式: postgresql://[username]:[password]@[host]:[port]/[database_name]
# 开发环境 (本地 PostgreSQL)
DATABASE_URL="postgresql://postgres:password@localhost:5432/augment_writer"
# 开发环境 (Docker)
# DATABASE_URL="postgresql://postgres:password@localhost:5432/augment_writer_dev"
# 生产环境示例
# DATABASE_URL="postgresql://augment_user:<EMAIL>:5432/augment_writer_prod"

REDIS_URL="redis://localhost:6379"
# Redis with password: redis://:password@localhost:6379
# Redis cloud: redis://username:password@host:port

# JWT配置
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# 服务器配置
PORT=8989
CLIENT_PORT=8181
NODE_ENV="development"
CORS_ORIGIN="http://localhost:8181"

# AI服务配置 - Gemini (主要)
GEMINI_API_KEY="your_gemini_api_key_here"
GEMINI_MODEL_FAST="gemini-2.0-flash"
GEMINI_MODEL_PRO="gemini-2.5-pro"
GEMINI_MODEL_LITE="gemini-2.0-flash-lite"
GEMINI_BASE_URL="https://generativelanguage.googleapis.com"

# AI服务配置 - Ollama (备用/本地)
OLLAMA_ENABLED="true"
OLLAMA_BASE_URL="http://localhost:11434"
OLLAMA_MODEL="llama3.2"
OLLAMA_TIMEOUT="30000"

# AI服务配置 - ComfyUI (图像生成)
COMFYUI_ENABLED="true"
COMFYUI_BASE_URL="http://localhost:8188"
COMFYUI_WORKFLOW_PATH="./workflows"
COMFYUI_OUTPUT_PATH="./generated_images"

# 文件存储配置
STORAGE_TYPE="local" # local, s3, minio
UPLOAD_PATH="./uploads"
MAX_FILE_SIZE="10485760" # 10MB

# S3/MinIO配置 (如果使用)
S3_ENDPOINT=""
S3_BUCKET=""
S3_ACCESS_KEY=""
S3_SECRET_KEY=""
S3_REGION="us-east-1"

# 应用配置
APP_NAME="AugmentWriter"
APP_VERSION="1.0.0"
LOG_LEVEL="info"

# 功能开关
FEATURE_COLLABORATION="true"
FEATURE_IMAGE_GENERATION="true"
FEATURE_EXPORT="true"
FEATURE_ANALYTICS="false"

# 限制配置
RATE_LIMIT_WINDOW="15" # 分钟
RATE_LIMIT_MAX="100" # 请求数
MAX_NOVEL_LENGTH="1000000" # 字符数
MAX_CHAPTERS_PER_NOVEL="1000"

# AI生成配置
AI_MAX_TOKENS="4000"
AI_TEMPERATURE="0.7"
AI_TOP_P="0.9"
AI_FREQUENCY_PENALTY="0.1"
AI_PRESENCE_PENALTY="0.1"

# 缓存配置
CACHE_TTL="3600" # 秒
CACHE_PREFIX="augment_writer:"

# 邮件配置 (可选)
SMTP_HOST=""
SMTP_PORT="587"
SMTP_USER=""
SMTP_PASS=""
FROM_EMAIL="<EMAIL>"
