version: '3.8'

services:
  # 开发环境数据库
  postgres-dev:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=augment_writer_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # 开发环境 Redis
  redis-dev:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  # Ollama 本地 AI 服务（可选）
  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    restart: unless-stopped
    profiles:
      - ai-local

  # ComfyUI 图像生成服务（可选）
  comfyui:
    image: comfyui/comfyui:latest
    ports:
      - "8188:8188"
    volumes:
      - comfyui_data:/app/ComfyUI
    environment:
      - COMFYUI_HOST=0.0.0.0
      - COMFYUI_PORT=8188
    restart: unless-stopped
    profiles:
      - ai-local

volumes:
  postgres_dev_data:
  redis_dev_data:
  ollama_data:
  comfyui_data:
