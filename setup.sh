#!/bin/bash

# AugmentWriter 项目安装脚本
# 用于快速设置开发环境

set -e

echo "🚀 开始安装 AugmentWriter 项目..."

# 检查 Node.js 版本
echo "📋 检查 Node.js 版本..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 18+ 版本"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js 版本过低，需要 18+ 版本，当前版本: $(node -v)"
    exit 1
fi

echo "✅ Node.js 版本检查通过: $(node -v)"

# 检查 npm 或 yarn
echo "📋 检查包管理器..."
if command -v yarn &> /dev/null; then
    PACKAGE_MANAGER="yarn"
    echo "✅ 使用 Yarn: $(yarn -v)"
elif command -v npm &> /dev/null; then
    PACKAGE_MANAGER="npm"
    echo "✅ 使用 npm: $(npm -v)"
else
    echo "❌ 未找到 npm 或 yarn"
    exit 1
fi

# 安装所有依赖（使用workspaces）
echo "📦 安装项目依赖..."
if [ "$PACKAGE_MANAGER" = "yarn" ]; then
    yarn install
else
    npm install
fi

# 创建环境配置文件
echo "⚙️  创建环境配置文件..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ 已创建 .env 文件，请根据需要修改配置"
else
    echo "⚠️  .env 文件已存在，跳过创建"
fi

# 检查数据库连接（可选）
echo "🗄️  数据库配置提醒..."
echo "请确保已安装并配置以下服务："
echo "  - PostgreSQL 14+"
echo "  - Redis 6+"
echo "  - 配置 .env 文件中的数据库连接信息"

# 检查 AI 服务配置
echo "🤖 AI 服务配置提醒..."
echo "请在 .env 文件中配置以下 AI 服务："
echo "  - GEMINI_API_KEY: Gemini API 密钥"
echo "  - OLLAMA_BASE_URL: Ollama 服务地址 (可选)"
echo "  - COMFYUI_BASE_URL: ComfyUI 服务地址 (可选)"

# 构建共享模块
echo "🔨 构建共享模块..."
if [ "$PACKAGE_MANAGER" = "yarn" ]; then
    yarn workspace augment-writer-shared build
else
    npm run build --workspace=shared
fi

echo ""
echo "🎉 安装完成！"
echo ""
echo "📝 下一步操作："
echo "1. 编辑 .env 文件，配置数据库和 AI 服务"
echo "2. 启动数据库服务 (PostgreSQL, Redis)"
echo "3. 运行数据库迁移: cd server && npm run db:migrate"
echo "4. 启动开发服务器: npm run dev"
echo ""
echo "🌐 访问地址："
echo "  - 前端: http://localhost:3000"
echo "  - 后端: http://localhost:3001"
echo ""
echo "📚 更多信息请查看 README.md"
