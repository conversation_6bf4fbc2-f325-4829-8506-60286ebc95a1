{"name": "augment-writer-shared", "version": "1.0.0", "description": "Shared types and utilities for AugmentWriter", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "vitest", "lint": "eslint . --ext ts --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts --fix"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.53.0", "typescript": "^5.2.2", "vitest": "^0.34.6"}}