// 导出所有类型和Schema
export * from './types/index.js'

// 导出常用的工具函数
export const API_ENDPOINTS = {
  // 用户相关
  AUTH_LOGIN: '/api/auth/login',
  AUTH_REGISTER: '/api/auth/register',
  AUTH_LOGOUT: '/api/auth/logout',
  AUTH_REFRESH: '/api/auth/refresh',
  USER_PROFILE: '/api/user/profile',
  
  // 小说相关
  NOVELS: '/api/novels',
  NOVEL_BY_ID: (id: string) => `/api/novels/${id}`,
  
  // 章节相关
  CHAPTERS: (novelId: string) => `/api/novels/${novelId}/chapters`,
  CHAPTER_BY_ID: (novelId: string, chapterId: string) => `/api/novels/${novelId}/chapters/${chapterId}`,
  
  // 大纲相关
  OUTLINES: (novelId: string) => `/api/novels/${novelId}/outlines`,
  OUTLINE_BY_ID: (novelId: string, outlineId: string) => `/api/novels/${novelId}/outlines/${outlineId}`,
  
  // 角色相关
  CHARACTERS: (novelId: string) => `/api/novels/${novelId}/characters`,
  CHARACTER_BY_ID: (novelId: string, characterId: string) => `/api/novels/${novelId}/characters/${characterId}`,
  
  // AI相关
  AI_GENERATE: '/api/ai/generate',
  AI_MODELS: '/api/ai/models',
  
  // 插图相关
  ILLUSTRATIONS: (novelId: string) => `/api/novels/${novelId}/illustrations`,
  ILLUSTRATION_BY_ID: (novelId: string, illustrationId: string) => `/api/novels/${novelId}/illustrations/${illustrationId}`,
} as const

// WebSocket事件类型常量
export const WS_EVENTS = {
  // 连接相关
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  
  // 协作编辑
  JOIN_NOVEL: 'join_novel',
  LEAVE_NOVEL: 'leave_novel',
  CONTENT_CHANGE: 'content_change',
  CURSOR_CHANGE: 'cursor_change',
  
  // AI生成
  AI_GENERATION_START: 'ai_generation_start',
  AI_GENERATION_PROGRESS: 'ai_generation_progress',
  AI_GENERATION_COMPLETE: 'ai_generation_complete',
  AI_GENERATION_ERROR: 'ai_generation_error',
  
  // 通知
  NOTIFICATION: 'notification',
  ERROR: 'error',
} as const

// 错误代码常量
export const ERROR_CODES = {
  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  
  // 验证错误
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  
  // 资源错误
  NOT_FOUND: 'NOT_FOUND',
  ALREADY_EXISTS: 'ALREADY_EXISTS',
  
  // 服务器错误
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  
  // AI服务错误
  AI_SERVICE_ERROR: 'AI_SERVICE_ERROR',
  AI_QUOTA_EXCEEDED: 'AI_QUOTA_EXCEEDED',
  AI_MODEL_UNAVAILABLE: 'AI_MODEL_UNAVAILABLE',
} as const

// 应用常量
export const APP_CONSTANTS = {
  // 分页
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // 文本限制
  MAX_NOVEL_TITLE_LENGTH: 200,
  MAX_CHAPTER_TITLE_LENGTH: 200,
  MAX_CHARACTER_NAME_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 2000,
  
  // AI生成限制
  MAX_AI_PROMPT_LENGTH: 2000,
  MAX_AI_CONTEXT_LENGTH: 10000,
  AI_GENERATION_TIMEOUT: 60000, // 60秒
  
  // 文件上传限制
  MAX_IMAGE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  
  // 缓存时间
  CACHE_TTL: {
    SHORT: 5 * 60, // 5分钟
    MEDIUM: 30 * 60, // 30分钟
    LONG: 24 * 60 * 60, // 24小时
  },
} as const

// Gemini模型配置
export const GEMINI_MODELS = {
  FAST: 'gemini-2.0-flash',
  PRO: 'gemini-2.5-pro',
  LITE: 'gemini-2.0-flash-lite',
} as const

// AI生成类型的中文映射
export const AI_GENERATION_TYPE_LABELS = {
  continue: '续写',
  rewrite: '重写',
  outline: '大纲生成',
  character: '角色生成',
  summary: '总结',
  image: '插图生成',
} as const
