import { z } from 'zod'

// ============================================================================
// 用户相关类型
// ============================================================================

export const UserSchema = z.object({
  id: z.string().uuid(),
  username: z.string().min(3).max(50),
  email: z.string().email(),
  avatar: z.string().url().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
  settings: z.record(z.any()).optional(),
})

export type User = z.infer<typeof UserSchema>

// ============================================================================
// 小说相关类型
// ============================================================================

export const NovelSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  title: z.string().min(1).max(200),
  description: z.string().max(2000).optional(),
  genre: z.string().max(50).optional(),
  styleSample: z.string().max(5000).optional(),
  coverImage: z.string().url().optional(),
  status: z.enum(['draft', 'writing', 'completed', 'published']),
  wordCount: z.number().int().min(0),
  chapterCount: z.number().int().min(0),
  createdAt: z.date(),
  updatedAt: z.date(),
  settings: z.record(z.any()).optional(),
})

export type Novel = z.infer<typeof NovelSchema>

// ============================================================================
// 章节相关类型
// ============================================================================

export const ChapterSchema = z.object({
  id: z.string().uuid(),
  novelId: z.string().uuid(),
  title: z.string().min(1).max(200),
  content: z.string(),
  orderIndex: z.number().int().min(0),
  status: z.enum(['draft', 'writing', 'completed']),
  wordCount: z.number().int().min(0),
  createdAt: z.date(),
  updatedAt: z.date(),
  summary: z.string().max(1000).optional(),
})

export type Chapter = z.infer<typeof ChapterSchema>

// ============================================================================
// 大纲相关类型
// ============================================================================

// 先定义基础类型
export interface OutlineNode {
  id: string
  title: string
  description?: string
  type: 'act' | 'chapter' | 'scene' | 'plot_point'
  orderIndex: number
  parentId?: string
  children?: OutlineNode[]
  status: 'planned' | 'writing' | 'completed'
  estimatedWordCount?: number
  actualWordCount?: number
}

// 然后定义 Schema
export const OutlineNodeSchema: z.ZodType<OutlineNode> = z.object({
  id: z.string().uuid(),
  title: z.string().min(1).max(200),
  description: z.string().max(1000).optional(),
  type: z.enum(['act', 'chapter', 'scene', 'plot_point']),
  orderIndex: z.number().int().min(0),
  parentId: z.string().uuid().optional(),
  children: z.array(z.lazy(() => OutlineNodeSchema)).optional(),
  status: z.enum(['planned', 'writing', 'completed']),
  estimatedWordCount: z.number().int().min(0).optional(),
  actualWordCount: z.number().int().min(0).optional(),
})

export const OutlineSchema = z.object({
  id: z.string().uuid(),
  novelId: z.string().uuid(),
  structure: z.array(OutlineNodeSchema),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export type Outline = z.infer<typeof OutlineSchema>

// ============================================================================
// 角色相关类型
// ============================================================================

export const CharacterSchema = z.object({
  id: z.string().uuid(),
  novelId: z.string().uuid(),
  name: z.string().min(1).max(100),
  description: z.string().max(2000).optional(),
  appearance: z.string().max(1000).optional(),
  personality: z.string().max(1000).optional(),
  background: z.string().max(2000).optional(),
  role: z.enum(['protagonist', 'antagonist', 'supporting', 'minor']),
  relationships: z.array(z.object({
    characterId: z.string().uuid(),
    relationship: z.string().max(100),
    description: z.string().max(500).optional(),
  })).optional(),
  avatar: z.string().url().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export type Character = z.infer<typeof CharacterSchema>

// ============================================================================
// AI相关类型
// ============================================================================

export const AIModelSchema = z.object({
  provider: z.enum(['gemini', 'ollama', 'comfyui']),
  model: z.string(),
  version: z.string().optional(),
  capabilities: z.array(z.enum(['text_generation', 'image_generation', 'analysis'])),
})

export type AIModel = z.infer<typeof AIModelSchema>

export const AIGenerationRequestSchema = z.object({
  type: z.enum(['continue', 'rewrite', 'outline', 'character', 'summary', 'image']),
  prompt: z.string().min(1),
  context: z.string().optional(),
  model: AIModelSchema.optional(),
  parameters: z.record(z.any()).optional(),
})

export type AIGenerationRequest = z.infer<typeof AIGenerationRequestSchema>

export const AIGenerationResponseSchema = z.object({
  id: z.string().uuid(),
  type: z.enum(['continue', 'rewrite', 'outline', 'character', 'summary', 'image']),
  result: z.string(),
  model: AIModelSchema,
  usage: z.object({
    promptTokens: z.number().int().min(0),
    completionTokens: z.number().int().min(0),
    totalTokens: z.number().int().min(0),
  }).optional(),
  createdAt: z.date(),
})

export type AIGenerationResponse = z.infer<typeof AIGenerationResponseSchema>

// AI服务提供者接口
export interface AIProvider {
  name: string
  isAvailable(): Promise<boolean>
  generateText(request: AIGenerationRequest): Promise<AIGenerationResponse>
  analyzeStyle(text: string): Promise<StyleAnalysis>
  generateOutline(synopsis: string, styleFeatures?: StyleAnalysis): Promise<OutlineStructure>
  summarizeText(text: string): Promise<TextSummary>
  smartContinue(content: string, context?: {
    previousChapters?: string[]
    characterProfiles?: Record<string, any>
    worldSettings?: Record<string, any>
    styleGuidelines?: StyleAnalysis
  }): Promise<AIGenerationResponse>
  getModelInfo(): AIModel
}

// 风格分析结果
export interface StyleAnalysis {
  tone: string
  style: string
  complexity: number
  vocabulary: string[]
  pacing: string
  themes: string[]
  patterns: Record<string, any>
  suggestions: string[]
}

// 大纲结构
export interface OutlineStructure {
  title: string
  synopsis: string
  structure: Array<{
    title: string
    description: string
    children: Array<{
      title: string
      summary: string
      estimatedWordCount: number
    }>
  }>
  acts: Array<{
    title: string
    description: string
    chapters: Array<{
      title: string
      summary: string
      estimatedWordCount: number
    }>
  }>
}

// 文本总结
export interface TextSummary {
  summary: string
  keyPoints: string[]
  characters: string[]
  unresolved: string[]
  wordCount: number
  readingTime: number
}

// 生成参数
export interface GenerationParameters {
  temperature?: number
  maxTokens?: number
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
}

// AI服务错误
export class AIServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public provider?: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'AIServiceError'
  }
}

// AI错误代码
export const AI_ERROR_CODES = {
  PROVIDER_UNAVAILABLE: 'PROVIDER_UNAVAILABLE',
  INVALID_REQUEST: 'INVALID_REQUEST',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  CONTENT_FILTERED: 'CONTENT_FILTERED',
  NETWORK_ERROR: 'NETWORK_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  GENERATION_FAILED: 'GENERATION_FAILED',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const

// ============================================================================
// 插图相关类型
// ============================================================================

export const IllustrationSchema = z.object({
  id: z.string().uuid(),
  novelId: z.string().uuid(),
  chapterId: z.string().uuid().optional(),
  title: z.string().max(200).optional(),
  description: z.string().max(1000),
  imageUrl: z.string().url(),
  thumbnailUrl: z.string().url().optional(),
  prompt: z.string().max(2000),
  model: z.string(),
  parameters: z.record(z.any()).optional(),
  createdAt: z.date(),
})

export type Illustration = z.infer<typeof IllustrationSchema>

// ============================================================================
// API响应类型
// ============================================================================

export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.any().optional(),
  }).optional(),
  meta: z.object({
    page: z.number().int().min(1).optional(),
    limit: z.number().int().min(1).optional(),
    total: z.number().int().min(0).optional(),
    totalPages: z.number().int().min(0).optional(),
  }).optional(),
})

export type ApiResponse<T = any> = {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  meta?: {
    page?: number
    limit?: number
    total?: number
    totalPages?: number
  }
}

// ============================================================================
// WebSocket事件类型
// ============================================================================

export const WebSocketEventSchema = z.object({
  type: z.string(),
  payload: z.any(),
  timestamp: z.date(),
  userId: z.string().uuid().optional(),
  novelId: z.string().uuid().optional(),
})

export type WebSocketEvent = z.infer<typeof WebSocketEventSchema>

// ============================================================================
// 导出所有Schema用于验证
// ============================================================================

export const schemas = {
  User: UserSchema,
  Novel: NovelSchema,
  Chapter: ChapterSchema,
  OutlineNode: OutlineNodeSchema,
  Outline: OutlineSchema,
  Character: CharacterSchema,
  AIModel: AIModelSchema,
  AIGenerationRequest: AIGenerationRequestSchema,
  AIGenerationResponse: AIGenerationResponseSchema,
  Illustration: IllustrationSchema,
  ApiResponse: ApiResponseSchema,
  WebSocketEvent: WebSocketEventSchema,
}
